# Supabase Configuration
SUPABASE_URL="your_supabase_url_here"
SUPABASE_ANON_KEY="your_supabase_anon_key_here"
SUPABASE_SERVICE_ROLE_KEY="your_supabase_service_role_key_here" # Keep this key secret and only use on the server!

# Paystack Configuration
PAYSTACK_SECRET_KEY="your_paystack_secret_key_here"

# Frontend URL (for Paystack callback)
FRONTEND_URL="http://localhost:3000" # Or your deployed frontend URL

# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmasterconfig).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.
