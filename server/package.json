{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "fix-db": "node migrations/fix_receipt_data_table.js", "fix-db-direct": "node migrations/fix_receipt_data_table_direct.js", "fix-db-simple": "node migrations/fix_receipt_data_table_simple.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@supabase/supabase-js": "^2.49.8", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "googleapis": "^149.0.0", "multer": "^2.0.0", "openai": "^5.0.1", "paystack-api": "^2.0.6"}, "devDependencies": {"supabase": "^2.24.3"}}