// Extract structured data from Tesseract OCR text
function extractDataFromTesseractText(text) {
  const data = {
    vendor: null,
    vendor_tax_id: null,
    date: null,
    currency: null,
    payment_method: 'Cash', // Default
    items: [],
    subtotal: null,
    tax_rate_percent: null,
    tax_amount: null,
    total_amount: null,
    source_file: null,
    category: 'Uncategorized' // Default category
  };

  // Extract vendor
  const vendorMatch = text.match(/(?:INVOICE|Receipt from|Bill from)\s+([A-Za-z0-9\s]+)/i) || 
                      text.match(/^([A-Za-z0-9\s]+)(?:\s+-\s+Invoice|\s+INVOICE)/im);
  if (vendorMatch) {
    data.vendor = vendorMatch[1].trim();
  }

  // Extract date
  const dateMatch = text.match(/DATE\s+([A-Za-z]+\s+\d{1,2},?\s+\d{4})/i) ||
                    text.match(/DATE\s+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i) ||
                    text.match(/(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/);
  if (dateMatch) {
    try {
      const dateStr = dateMatch[1];
      // Parse date and format as YYYY-MM-DD
      const parsedDate = new Date(dateStr);
      if (!isNaN(parsedDate.getTime())) {
        data.date = parsedDate.toISOString().split('T')[0];
      }
    } catch (e) {
      console.error('Date parsing error:', e);
    }
  }

  // Extract currency and total amount - prioritize the TOTAL line, not SUBTOTAL
  const totalMatch = text.match(/TOTAL\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                     text.match(/Total\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                     text.match(/Amount\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                     text.match(/BALANCE DUE\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  
  console.log('Amount extraction debug - totalMatch:', totalMatch);
  
  if (totalMatch) {
    data.currency = totalMatch[1] || totalMatch[2]?.trim() || 'USD';
    // Clean currency symbol
    data.currency = data.currency.replace(/[^A-Z]/gi, '');
    if (!data.currency) data.currency = 'USD';
    
    // Clean and parse amount
    const amountStr = totalMatch[3].replace(/,/g, '');
    const amount = parseFloat(amountStr);
    console.log('Amount extraction debug - extracted amount:', amount);
    if (!isNaN(amount)) {
      data.total_amount = amount;
    }
  }

  // Extract subtotal
  const subtotalMatch = text.match(/SUBTOTAL\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                        text.match(/Sub\s*total\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  if (subtotalMatch) {
    const subtotalStr = subtotalMatch[3].replace(/,/g, '');
    const subtotal = parseFloat(subtotalStr);
    if (!isNaN(subtotal)) {
      data.subtotal = subtotal;
    }
  } else if (data.total_amount) {
    // If no subtotal found, use total as fallback
    data.subtotal = data.total_amount;
  }

  // Extract tax amount and rate
  const taxMatch = text.match(/TAX\s+\((\d+)%\)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                   text.match(/TAX\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                   text.match(/VAT\s+\((\d+)%\)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  if (taxMatch) {
    // Check if we have a tax rate percentage
    if (taxMatch[1]) {
      data.tax_rate_percent = parseFloat(taxMatch[1]);
      const taxAmountStr = taxMatch[4] ? taxMatch[4].replace(/,/g, '') : taxMatch[3].replace(/,/g, '');
      const taxAmount = parseFloat(taxAmountStr);
      if (!isNaN(taxAmount)) {
        data.tax_amount = taxAmount;
      }
    } else {
      // No percentage in the match
      const taxAmountStr = taxMatch[3] ? taxMatch[3].replace(/,/g, '') : '';
      const taxAmount = parseFloat(taxAmountStr);
      if (!isNaN(taxAmount)) {
        data.tax_amount = taxAmount;
        // Calculate tax rate if we have subtotal
        if (data.subtotal && data.subtotal > 0) {
          data.tax_rate_percent = (taxAmount / data.subtotal) * 100;
        }
      }
    }
  }

  // Extract payment method
  const paymentMethodMatch = text.match(/Payment\s+Method\s*:?\s*([A-Za-z0-9\s]+)/i) ||
                             text.match(/Paid\s+via\s+([A-Za-z0-9\s]+)/i) ||
                             text.match(/DUE\s+([A-Za-z0-9\s]+)/i);
  if (paymentMethodMatch) {
    data.payment_method = paymentMethodMatch[1].trim();
  }

  // Extract items
  const itemsSection = text.match(/DESCRIPTION.*\n([\s\S]*?)(?:SUBTOTAL|SUB\s*TOTAL|TOTAL)/i);
  if (itemsSection) {
    const itemsText = itemsSection[1];
    const itemLines = itemsText.split('\n').filter(line => line.trim());
    
    itemLines.forEach(line => {
      // Try to match item with price, quantity, and amount
      const itemMatch = line.match(/([^0-9]+)\s+([A-Z]{1,3})?\s*([^0-9]*)([0-9,.]+)(?:\s+(\d+)\s+([A-Z]{1,3})?\s*([^0-9]*)([0-9,.]+))?/);
      if (itemMatch) {
        const description = itemMatch[1].trim();
        let total = 0;
        
        // If we have quantity and amount
        if (itemMatch[8]) {
          const amountStr = itemMatch[8].replace(/,/g, '');
          total = parseFloat(amountStr);
        } else {
          // Just use the price
          const priceStr = itemMatch[4].replace(/,/g, '');
          total = parseFloat(priceStr);
        }
        
        if (!isNaN(total) && total > 0) {
          data.items.push({
            description: description,
            total: total,
            category: 'Uncategorized'
          });
        }
      }
    });
  }

  // If no items were extracted but we have a total, create a default item
  if (data.items.length === 0 && data.total_amount) {
    data.items.push({
      description: 'Unspecified Item',
      total: data.total_amount,
      category: 'Uncategorized'
    });
  }

  return data;
}

// Categorize receipt items using OpenAI
async function categorizeItemsWithOpenAI(items) {
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const itemDescriptions = items.map(item => item.description).join(', ');
    
    const prompt = `
      You are an expert in categorizing receipt items. Please categorize the following items into appropriate expense categories:
      
      Items: ${itemDescriptions}
      
      Choose from these categories:
      - Groceries
      - Restaurant
      - Office Supplies
      - Utilities
      - Transportation
      - Accommodation
      - Entertainment
      - Medical
      - Education
      - Technology
      - Clothing
      - Household
      - Personal Care
      - Gifts
      - Miscellaneous
      
      Return a JSON array with each item and its category, like this:
      [
        {"description": "item1", "category": "category1"},
        {"description": "item2", "category": "category2"}
      ]
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 1024,
    });

    const content = response.choices[0].message.content;
    
    // Parse the JSON
    const categorizedItems = JSON.parse(content);
    
    // Create a mapping of description to category
    const categoryMap = {};
    if (categorizedItems && Array.isArray(categorizedItems)) {
      categorizedItems.forEach(item => {
        if (item.description) {
          categoryMap[item.description.toLowerCase()] = item.category;
        }
      });
    } else if (categorizedItems && categorizedItems.items && Array.isArray(categorizedItems.items)) {
      categorizedItems.items.forEach(item => {
        if (item.description) {
          categoryMap[item.description.toLowerCase()] = item.category;
        }
      });
    }
    
    // Update the original items with categories
    return items.map(item => {
      const lowerDesc = item.description.toLowerCase();
      return {
        ...item,
        category: categoryMap[lowerDesc] || item.category || 'Uncategorized'
      };
    });
  } catch (error) {
    console.error('Error categorizing items with OpenAI:', error);
    return items; // Return original items if categorization fails
  }
}

// Process receipt with OpenAI Vision
async function processReceiptWithOpenAI(imageUrl) {
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      You are an expert receipt analyzer. Extract the following information from this receipt image with high accuracy:
      
      - Vendor name (the business that issued the receipt)
      - Vendor tax ID (if available)
      - Date (in YYYY-MM-DD format)
      - Category (e.g., Groceries, Restaurant, Office Supplies, Utilities, Transportation)
      - Payment method (e.g., Cash, Credit Card, etc.)
      - Currency (e.g., USD, EUR, KSh)
      - Subtotal (before tax)
      - Tax rate percentage (if available)
      - Tax amount
      - Total amount (final amount paid)
      - Individual items with:
        - Description (product or service name)
        - Total price for that item
        - Category for that item (if available)
      
      Be very precise with numerical values. If you see conflicting information, use the most likely correct value based on context.
      
      Format the response as a valid JSON object with these fields:
      {
        "vendor": "string",
        "vendor_tax_id": "string or null",
        "date": "YYYY-MM-DD",
        "category": "string",
        "payment_method": "string",
        "currency": "string",
        "subtotal": number,
        "tax_rate_percent": number or null,
        "tax_amount": number,
        "total_amount": number,
        "items": [
          {
            "description": "string",
            "total": number,
            "category": "string"
          }
        ]
      }
      
      If any field is not found, use null for strings and 0 for numbers. Ensure the JSON is valid.
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: imageUrl,
              },
            },
          ],
        },
      ],
      max_tokens: 4096,
    });

    const content = response.choices[0].message.content;
    
    // Extract JSON from the response
    const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/```([\s\S]*?)```/) || [null, content];
    const jsonString = jsonMatch[1] || content;
    
    // Clean up the JSON string
    const cleanedJsonString = jsonString.replace(/^```json\n|^```|\n```$/g, '').trim();
    
    // Parse the JSON
    const extractedData = JSON.parse(cleanedJsonString);
    
    // Ensure items is an array
    if (!Array.isArray(extractedData.items)) {
      extractedData.items = [];
    }
    
    // Ensure each item has the required fields
    extractedData.items = extractedData.items.map(item => ({
      description: item.description || 'Unspecified Item',
      total: parseFloat(item.total || 0),
      category: item.category || 'Uncategorized'
    }));
    
    // If items have no categories, try to categorize them
    const hasCategories = extractedData.items.some(item => item.category && item.category !== 'Uncategorized');
    if (!hasCategories && extractedData.items.length > 0) {
      extractedData.items = await categorizeItemsWithOpenAI(extractedData.items);
    }
    
    // Ensure numerical values are properly parsed
    extractedData.subtotal = parseFloat(extractedData.subtotal || 0);
    extractedData.tax_rate_percent = parseFloat(extractedData.tax_rate_percent || 0);
    extractedData.tax_amount = parseFloat(extractedData.tax_amount || 0);
    extractedData.total_amount = parseFloat(extractedData.total_amount || 0);
    
    return {
      success: true,
      extractedData
    };
  } catch (error) {
    console.error('Error processing receipt with OpenAI:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Export the functions
module.exports = {
  extractDataFromTesseractText,
  processReceiptWithOpenAI,
  categorizeItemsWithOpenAI
};
