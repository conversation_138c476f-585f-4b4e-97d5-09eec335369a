require('dotenv').config({ path: '../.env' }); // Specify path to .env in the root directory
const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');

console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Loaded' : 'Not Loaded'); // Log service role key status

const multer = require('multer');
const OpenAI = require('openai');
const axios = require('axios'); // Add axios for HTTP requests
const Tesseract = require('tesseract.js'); // Add tesseract.js for OCR

// Add this near the top with other constants
const FREE_TIER_LIMIT = 5; // Maximum receipts for free tier

// Pricing Tiers - Define at the top so it's available when used
const PRICING_TIERS = {
  FREE: { maxReceipts: FREE_TIER_LIMIT }, // Max receipts for free tier (for demo purposes)
  TIER_1: { maxReceipts: 200, price: 5000 }, // Price in kobo (50 USD)
  TIER_2: { minReceipts: 201, price: 10000 }, // Price in kobo (100 USD)
};

// Enhanced receipt data extraction function
function extractDataFromTesseractText(text) {
  const data = {
    vendor: null,
    vendor_tax_id: null,
    date: null,
    currency: null,
    payment_method: 'Cash', // Default
    items: [],
    subtotal: null,
    tax_rate_percent: null,
    tax_amount: null,
    total_amount: null,
    source_file: null,
    category: 'Uncategorized' // Default category
  };

  // Extract vendor
  const vendorMatch = text.match(/(?:INVOICE|Receipt from|Bill from)\s+([A-Za-z0-9\s]+)/i) || 
                      text.match(/^([A-Za-z0-9\s]+)(?:\s+-\s+Invoice|\s+INVOICE)/im);
  if (vendorMatch) {
    data.vendor = vendorMatch[1].trim();
  }

  // Extract date
  const dateMatch = text.match(/DATE\s+([A-Za-z]+\s+\d{1,2},?\s+\d{4})/i) ||
                    text.match(/DATE\s+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i) ||
                    text.match(/(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/);
  if (dateMatch) {
    try {
      const dateStr = dateMatch[1];
      // Parse date and format as YYYY-MM-DD
      const parsedDate = new Date(dateStr);
      if (!isNaN(parsedDate.getTime())) {
        data.date = parsedDate.toISOString().split('T')[0];
      }
    } catch (e) {
      console.error('Date parsing error:', e);
    }
  }

  // Extract currency and total amount
  const totalMatch = text.match(/TOTAL\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                     text.match(/Total\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                     text.match(/Amount\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  
  console.log('Amount extraction debug - totalMatch:', totalMatch);
  
  if (totalMatch) {
    data.currency = totalMatch[1] || totalMatch[2]?.trim() || 'USD';
    // Clean currency symbol
    data.currency = data.currency.replace(/[^A-Z]/gi, '');
    if (!data.currency) data.currency = 'USD';
    
    // Clean and parse amount
    const amountStr = totalMatch[3].replace(/,/g, '');
    const amount = parseFloat(amountStr);
    console.log('Amount extraction debug - extracted amount:', amount);
    if (!isNaN(amount)) {
      data.total_amount = amount;
    }
  }

  // Extract subtotal
  const subtotalMatch = text.match(/SUBTOTAL\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                        text.match(/Sub\s*total\s*:?\s*([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  if (subtotalMatch) {
    const subtotalStr = subtotalMatch[3].replace(/,/g, '');
    const subtotal = parseFloat(subtotalStr);
    if (!isNaN(subtotal)) {
      data.subtotal = subtotal;
    }
  } else if (data.total_amount) {
    // If no subtotal found, use total as fallback
    data.subtotal = data.total_amount;
  }

  // Extract tax amount and rate
  const taxMatch = text.match(/TAX\s+\((\d+)%\)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                   text.match(/TAX\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i) ||
                   text.match(/VAT\s+\((\d+)%\)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)/i);
  if (taxMatch) {
    // Check if we have a tax rate percentage
    if (taxMatch[1]) {
      data.tax_rate_percent = parseFloat(taxMatch[1]);
      const taxAmountStr = taxMatch[4].replace(/,/g, '');
      const taxAmount = parseFloat(taxAmountStr);
      if (!isNaN(taxAmount)) {
        data.tax_amount = taxAmount;
      }
    } else {
      // No percentage in the match
      const taxAmountStr = taxMatch[3].replace(/,/g, '');
      const taxAmount = parseFloat(taxAmountStr);
      if (!isNaN(taxAmount)) {
        data.tax_amount = taxAmount;
        // Calculate tax rate if we have subtotal
        if (data.subtotal && data.subtotal > 0) {
          data.tax_rate_percent = (taxAmount / data.subtotal) * 100;
        }
      }
    }
  }

  // Extract items
  const itemsSection = text.match(/DESCRIPTION.*\n([\s\S]*?)(?:SUBTOTAL|SUB\s*TOTAL|TOTAL)/i);
  if (itemsSection) {
    const itemsText = itemsSection[1];
    const itemLines = itemsText.split('\n').filter(line => line.trim());
    
    itemLines.forEach(line => {
      // Try to match item with price, quantity, and amount
      const itemMatch = line.match(/([^0-9]+)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+)(?:\s+(\d+)\s+([A-Z]{1,3})?([^0-9]*)([0-9,.]+))?/);
      if (itemMatch) {
        const description = itemMatch[1].trim();
        let total = 0;
        
        // If we have quantity and amount
        if (itemMatch[8]) {
          const amountStr = itemMatch[8].replace(/,/g, '');
          total = parseFloat(amountStr);
        } else {
          // Just use the price
          const priceStr = itemMatch[4].replace(/,/g, '');
          total = parseFloat(priceStr);
        }
        
        if (!isNaN(total) && total > 0) {
          data.items.push({
            description: description,
            total: total,
            category: 'Uncategorized'
          });
        }
      }
    });
  }

  // If no items were extracted but we have a total, create a default item
  if (data.items.length === 0 && data.total_amount) {
    data.items.push({
      description: 'Unspecified Item',
      total: data.total_amount,
      category: 'Uncategorized'
    });
  }

  return data;
}

// Function to process receipt with Tesseract OCR
async function processReceiptWithTesseract(imageBuffer) {
  try {
    const { data: { text, confidence } } = await Tesseract.recognize(
      imageBuffer,
      'eng', // English language
      { logger: m => console.log(m) } // Log progress
    );

    console.log('Tesseract Confidence:', confidence);
    console.log('Tesseract Text:', text);

    // Define a confidence threshold for "clear enough"
    const CONFIDENCE_THRESHOLD = 80; // Adjust as needed

    if (confidence >= CONFIDENCE_THRESHOLD) {
      const extractedData = extractDataFromTesseractText(text);
      // Check if essential fields are extracted
      if (extractedData.vendor && extractedData.total_amount) {
        return { extractedData, confidence, success: true };
      }
    }
    return { success: false, confidence, text };
  } catch (err) {
    console.error('Tesseract OCR error:', err);
    return { success: false, error: err.message };
  }
}

// Function to process receipt with OpenAI Vision
async function processReceiptWithOpenAI(imageUrl) {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: "Extract structured data from this receipt: vendor, amount, currency, date (YYYY-MM-DD), category (e.g., Food, Transport, Utilities), payment_method, and a list of items with their names and prices. Respond in JSON format." },
            { type: "image_url", image_url: { url: imageUrl } },
          ],
        },
      ],
      response_format: { type: "json_object" },
    });

    const extractedData = JSON.parse(completion.choices[0].message.content);
    return { extractedData, success: true };
  } catch (openaiErr) {
    console.error('OpenAI error:', openaiErr);
    return { success: false, error: openaiErr.message };
  }
}

const app = express();
const PORT = process.env.PORT || 5000;

const supabase = createClient( // Used for client-side operations (e.g., auth)
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

const supabaseAdmin = createClient( // Used for server-side operations that bypass RLS
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configure Multer for file uploads
const upload = multer({ storage: multer.memoryStorage() });

app.use(cors());
app.use(express.json());

// Middleware to protect routes
const protect = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ error: 'No authorization token provided.' });
    }

    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: error?.message || 'Invalid or expired token.' });
    }

    req.user = user;
    next();
  } catch (err) {
    console.error('Authentication error:', err.message);
    return res.status(500).json({ error: 'Internal server error during authentication.' });
  }
};

app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'RECO AI backend running' });
});

// Example of a protected route
app.get('/api/protected-test', protect, async (req, res) => {
  res.json({ message: 'You accessed a protected route!', user: req.user });
});

// Helper to ensure user profile exists (using supabaseAdmin to bypass RLS for this critical function)
async function ensureUserProfile(user) {
  // Try to fetch profile using supabaseAdmin
  let { data: profileData, error: profileError } = await supabaseAdmin
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError && profileError.code === 'PGRST116') {
    // No profile row, insert default (match schema) using supabaseAdmin
    const defaultProfile = {
      id: user.id,
      full_name: user.email || '',
      avatar_url: '',
      created_at: new Date().toISOString(),
      role: 'Freelancer',
      tier: 'free', // Default to free tier
      receipt_count: 0,
      payment_status: 'inactive',
    };
    const { error: insertError } = await supabaseAdmin
      .from('profiles')
      .insert([defaultProfile]);
    if (insertError) {
      console.error('Error inserting default profile:', insertError);
      throw insertError;
    }
    // Try fetching again after insert
    ({ data: profileData, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single());
  }
  if (profileError) {
    console.error('Error fetching profile after ensure:', profileError);
    throw profileError;
  }
  return profileData;
}

// Receipt Upload and Processing Endpoint
app.post('/api/upload-receipt', protect, upload.single('receipt'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }

    // Fetch user profile to check tier and receipt count for enforcement
    let profileData;
    try {
      profileData = await ensureUserProfile(req.user);
    } catch (profileError) {
      console.error('Error ensuring profile for enforcement:', profileError);
      return res.status(500).json({ error: 'Failed to retrieve or create user profile for enforcement.', details: profileError.message });
    }
    const { receipt_count, tier, payment_status } = profileData;

    // Enforcement logic: Allow free tier for demo purposes up to a limit
    if (tier === 'free') {
      if (receipt_count >= FREE_TIER_LIMIT) {
        return res.status(403).json({ 
          error: `You've reached your free tier limit of ${FREE_TIER_LIMIT} receipts.`, 
          upgradeRequired: true,
          receiptsProcessed: receipt_count,
          maxReceipts: FREE_TIER_LIMIT
        });
      }
    } else if (payment_status !== 'paid') {
      // For TIER_1 or TIER_2, payment is required
      return res.status(403).json({ 
        error: 'Payment required to upload receipts for your current tier.',
        upgradeRequired: true
      });
    }

    if (tier === 'TIER_1' && receipt_count >= PRICING_TIERS.TIER_1.maxReceipts) {
      return res.status(403).json({ error: `You have reached your Tier 1 limit of ${PRICING_TIERS.TIER_1.maxReceipts} receipts. Please upgrade to Tier 2.` });
    }

    const imageBuffer = req.file.buffer;
    const base64Image = imageBuffer.toString('base64');
    const imageUrl = `data:${req.file.mimetype};base64,${base64Image}`;

    let extractedData;
    let processingMethod;

    // For paid tiers, prioritize OpenAI for better accuracy
    if (tier !== 'free') {
      // Start with OpenAI Vision for better accuracy
      const openaiResult = await processReceiptWithOpenAI(imageUrl);
      if (openaiResult.success) {
        extractedData = openaiResult.extractedData;
        processingMethod = 'OpenAI Vision';
        console.log('Receipt processed with OpenAI Vision.');
      } else {
        // Fallback to Tesseract if OpenAI fails
        console.log('OpenAI Vision failed, falling back to Tesseract OCR.');
        const tesseractResult = await processReceiptWithTesseract(imageBuffer);
        if (tesseractResult.success) {
          extractedData = tesseractResult.extractedData;
          processingMethod = 'Tesseract OCR';
          console.log('Receipt processed with Tesseract OCR.');
        } else {
          console.error('Both OpenAI and Tesseract failed:', tesseractResult.error);
          return res.status(500).json({ error: 'Failed to process receipt with both OpenAI and Tesseract.', details: tesseractResult.error });
        }
      }
    } else {
      // For free tier, start with Tesseract to save costs
      const tesseractResult = await processReceiptWithTesseract(imageBuffer);
      if (tesseractResult.success) {
        extractedData = tesseractResult.extractedData;
        processingMethod = 'Tesseract OCR';
        console.log('Receipt processed with Tesseract OCR.');
      } else {
        // Fallback to OpenAI if Tesseract fails or confidence is low
        console.log('Tesseract OCR failed or confidence too low, falling back to OpenAI Vision.');
        const openaiResult = await processReceiptWithOpenAI(imageUrl);
        if (openaiResult.success) {
          extractedData = openaiResult.extractedData;
          processingMethod = 'OpenAI Vision';
          console.log('Receipt processed with OpenAI Vision.');
        } else {
          console.error('OpenAI Vision also failed:', openaiResult.error);
          return res.status(500).json({ error: 'Failed to process receipt with both Tesseract and OpenAI.', details: openaiResult.error });
        }
      }
    }

    // Map extracted data to Supabase receipt_data schema with null-safe defaults
    const receiptData = {
      user_id: req.user.id,
      vendor: extractedData.vendor || 'Unknown Vendor',
      vendor_tax_id: extractedData.vendor_tax_id || null,
      date: extractedData.date || new Date().toISOString().split('T')[0],
      category: extractedData.category || 'Uncategorized',
      payment_method: extractedData.payment_method || 'Cash',
      items: Array.isArray(extractedData.items) ? extractedData.items.map(item => ({
        description: item.description || item.name || 'Unspecified Item',
        total: parseFloat(item.total || item.price || 0),
        category: item.category || 'Uncategorized'
      })) : [{ 
        description: 'Unspecified Item', 
        total: extractedData.total_amount || extractedData.amount || 0, 
        category: 'Uncategorized' 
      }],
      subtotal: extractedData.subtotal || extractedData.total_amount || extractedData.amount || 0,
      tax_rate_percent: extractedData.tax_rate_percent || 0,
      tax_amount: extractedData.tax_amount || 0,
      total_amount: extractedData.total_amount || extractedData.amount || 0,
      source_file: req.file.originalname || 'uploaded_receipt.jpg',
      created_at: new Date().toISOString(),
      currency: extractedData.currency || 'USD'
    };

    console.log('Attempting to save receiptData:', JSON.stringify(receiptData, null, 2));

    // Use supabaseAdmin instead of supabase for database operations
    const { data, error } = await supabaseAdmin
      .from('receipt_data')
      .insert([receiptData])
      .select();

    if (error) {
      console.error('Error saving receipt to Supabase:', JSON.stringify(error, null, 2));
      return res.status(500).json({ error: 'Failed to save receipt data.', details: error.message });
    }

    // Increment receipt_count for the user
    const newReceiptCount = (receipt_count || 0) + 1;
    try {
      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update({ receipt_count: newReceiptCount })
        .eq('id', req.user.id);
      if (updateError) {
        console.error('Error updating receipt_count:', updateError);
      }
    } catch (updateErr) {
      console.error('Error updating receipt_count:', updateErr);
    }

    res.status(200).json({ 
      message: 'Receipt processed and saved successfully!', 
      extractedData, 
      savedReceipt: data[0],
      processingMethod 
    });

  } catch (err) {
    console.error('Error processing receipt:', err);
    res.status(500).json({ error: 'Failed to process receipt.', details: err.message });
  }
});

app.get('/api/supabase-test', async (req, res) => {
  // Try to fetch the current user's profile (public test)
  const { data, error } = await supabase.from('profiles').select('*').limit(1);
  if (error) {
    return res.status(500).json({ error: error.message });
  }
  res.json({ data });
});

// Google Drive Receipt Import Endpoint
app.post('/api/import-from-drive', protect, async (req, res) => {
  try {
    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'No file ID provided.' });
    }

    // Get user profile for enforcement
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', req.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile for enforcement:', profileError);
      return res.status(500).json({ error: 'Failed to retrieve user profile for enforcement.' });
    }

    const { receipt_count, tier, payment_status } = profileData;

    // Enforcement logic for Google Drive: Allow free tier for demo purposes up to a limit
    if (tier === 'free') {
      if (receipt_count >= PRICING_TIERS.FREE.maxReceipts) {
        return res.status(403).json({ error: `You have reached your free tier limit of ${PRICING_TIERS.FREE.maxReceipts} receipts. Please upgrade to a paid tier to continue importing from Google Drive.` });
      }
    } else if (payment_status !== 'paid') {
      // For TIER_1 or TIER_2, payment is required
      return res.status(403).json({ error: 'Payment required to import receipts from Google Drive for your current tier.' });
    }

    // Get Google Drive auth
    const auth = await getGoogleDriveAuth(req.user.id);
    if (!auth) {
      return res.status(401).json({ error: 'Google Drive authorization required.' });
    }

    // Get file metadata and content
    const drive = google.drive({ version: 'v3', auth });
    const { data: fileMetadata } = await drive.files.get({
      fileId,
      fields: 'name,mimeType'
    });

    const fileName = fileMetadata.name;
    const mimeType = fileMetadata.mimeType;

    // Check if file is an image
    if (!mimeType.startsWith('image/')) {
      return res.status(400).json({ error: 'File must be an image.' });
    }

    // Download file content
    const response = await drive.files.get({
      fileId,
      alt: 'media'
    }, { responseType: 'arraybuffer' });

    const fileBuffer = Buffer.from(response.data);
    const base64Image = fileBuffer.toString('base64');
    const imageUrl = `data:${mimeType};base64,${base64Image}`;

    let extractedData;
    let processingMethod = 'OpenAI Vision'; // Default to OpenAI

    // 1. Try Tesseract OCR first for Google Drive images
    const tesseractResult = await processReceiptWithTesseract(fileBuffer);

    if (tesseractResult.success) {
      extractedData = tesseractResult.extractedData;
      processingMethod = 'Tesseract OCR';
      console.log('Google Drive receipt processed with Tesseract OCR.');
    } else {
      console.log('Tesseract OCR failed or confidence too low for Google Drive receipt, falling back to OpenAI Vision.');
      // 2. Fallback to OpenAI Vision if Tesseract fails or confidence is low
      const openaiResult = await processReceiptWithOpenAI(imageUrl);
      if (openaiResult.success) {
        extractedData = openaiResult.extractedData;
        processingMethod = 'OpenAI Vision';
        console.log('Google Drive receipt processed with OpenAI Vision.');
      } else {
        console.error('OpenAI Vision also failed for Google Drive receipt:', openaiResult.error);
        return res.status(500).json({ error: 'Failed to process Google Drive receipt with both Tesseract and OpenAI.', details: openaiResult.error });
      }
    }

    // Save to Supabase using the new receipt_data table
    const receiptData = {
      user_id: req.user.id,
      vendor: extractedData.vendor || 'Unknown Vendor',
      vendor_tax_id: extractedData.vendor_tax_id || null,
      date: extractedData.date || new Date().toISOString().split('T')[0],
      category: extractedData.category || 'Uncategorized',
      payment_method: extractedData.payment_method || 'Cash',
      items: Array.isArray(extractedData.items) ? extractedData.items.map(item => ({
        description: item.description || item.name || 'Unspecified Item',
        total: parseFloat(item.total || item.price || 0),
        category: item.category || 'Uncategorized'
      })) : [{ 
        description: 'Unspecified Item', 
        total: extractedData.total_amount || extractedData.amount || 0, 
        category: 'Uncategorized' 
      }],
      subtotal: extractedData.subtotal || extractedData.total_amount || extractedData.amount || 0,
      tax_rate_percent: extractedData.tax_rate_percent || 0,
      tax_amount: extractedData.tax_amount || 0,
      total_amount: extractedData.total_amount || extractedData.amount || 0,
      source_file: fileName,
      created_at: new Date().toISOString(),
      currency: extractedData.currency || 'USD'
    };

    const { data, error } = await supabaseAdmin
      .from('receipt_data')
      .insert([receiptData])
      .select();

    if (error) {
      console.error('Error saving Google Drive receipt to Supabase:', error);
      return res.status(500).json({ error: 'Failed to save receipt data.', details: error.message });
    }

    // Increment receipt_count for the user
    const newReceiptCount = (receipt_count || 0) + 1;
    try {
      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update({ receipt_count: newReceiptCount })
        .eq('id', req.user.id);
      if (updateError) {
        console.error('Error updating receipt_count:', updateError);
      }
    } catch (updateErr) {
      console.error('Error updating receipt_count:', updateErr);
    }

    res.status(200).json({ 
      message: 'Google Drive receipt processed and saved successfully!', 
      extractedData, 
      savedReceipt: data[0],
      processingMethod 
    });

  } catch (err) {
    console.error('Error processing Google Drive receipt:', err);
    res.status(500).json({ error: 'Failed to process Google Drive receipt.', details: err.message });
  }
});

// Google Sheets Export Endpoint
app.post('/api/export-to-sheets', protect, async (req, res) => {
  try {
    // Get receipts for the user from the receipt_data table
    const { data: receipts, error } = await supabaseAdmin
      .from('receipt_data')
      .select('*')
      .eq('user_id', req.user.id)
      .order('date', { ascending: false });

    if (error) {
      return res.status(500).json({ error: 'Failed to fetch receipts.', details: error.message });
    }

    if (!receipts || receipts.length === 0) {
      return res.status(404).json({ error: 'No receipts found for export.' });
    }

    // Get Google Sheets auth
    const auth = await getGoogleSheetsAuth(req.user.id);
    if (!auth) {
      return res.status(401).json({ error: 'Google Sheets authorization required.' });
    }

    const sheets = google.sheets({ version: 'v4', auth });

    // Create a new spreadsheet
    const spreadsheet = await sheets.spreadsheets.create({
      resource: {
        properties: {
          title: `RECO AI Receipts Export - ${new Date().toLocaleDateString()}`
        }
      }
    });

    const spreadsheetId = spreadsheet.data.spreadsheetId;

    // Define headers for the sheet
    const headers = [
      'Receipt ID',
      'Vendor',
      'Vendor Tax ID',
      'Date',
      'Currency',
      'Payment Method',
      'Item Description',
      'Category',
      'Item Amount',
      'Subtotal',
      'Tax Rate %',
      'Tax Amount',
      'Total Amount',
      'Source File'
    ];

    // Prepare rows for the sheet - one row per item
    const rows = [];
    rows.push(headers);

    receipts.forEach(receipt => {
      // If receipt has items, create one row per item
      if (receipt.items && receipt.items.length > 0) {
        receipt.items.forEach(item => {
          rows.push([
            receipt.id,
            receipt.vendor || 'Unknown',
            receipt.vendor_tax_id || '',
            receipt.date || '',
            receipt.currency || '',
            receipt.payment_method || 'Cash',
            item.description || '',
            item.category || 'Uncategorized',
            item.total || 0,
            receipt.subtotal || 0,
            receipt.tax_rate_percent || 0,
            receipt.tax_amount || 0,
            receipt.total_amount || 0,
            receipt.source_file || ''
          ]);
        });
      } else {
        // If no items, create a single row
        rows.push([
          receipt.id,
          receipt.vendor || 'Unknown',
          receipt.vendor_tax_id || '',
          receipt.date || '',
          receipt.currency || '',
          receipt.payment_method || 'Cash',
          'Unspecified Item',
          'Uncategorized',
          receipt.total_amount || 0,
          receipt.subtotal || 0,
          receipt.tax_rate_percent || 0,
          receipt.tax_amount || 0,
          receipt.total_amount || 0,
          receipt.source_file || ''
        ]);
      }
    });

    // Update the spreadsheet with the data
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: 'Sheet1!A1',
      valueInputOption: 'USER_ENTERED',
      resource: {
        values: rows
      }
    });

    // Format the spreadsheet
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      resource: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: 0,
                startRowIndex: 0,
                endRowIndex: 1
              },
              cell: {
                userEnteredFormat: {
                  backgroundColor: {
                    red: 0.2,
                    green: 0.2,
                    blue: 0.2
                  },
                  textFormat: {
                    bold: true,
                    foregroundColor: {
                      red: 1.0,
                      green: 1.0,
                      blue: 1.0
                    }
                  }
                }
              },
              fields: 'userEnteredFormat(backgroundColor,textFormat)'
            }
          },
          {
            autoResizeDimensions: {
              dimensions: {
                sheetId: 0,
                dimension: 'COLUMNS',
                startIndex: 0,
                endIndex: 14
              }
            }
          }
        ]
      }
    });

    res.status(200).json({
      message: 'Receipts exported successfully.',
      spreadsheetId,
      spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}`
    });
  } catch (err) {
    console.error('Error exporting receipts to Google Sheets:', err);
    res.status(500).json({ error: 'Failed to export receipts to Google Sheets.', details: err.message });
  }
});


// Paystack Initiate Transaction Endpoint
app.post('/api/paystack/initiate', protect, async (req, res) => {
  const { email, amount } = req.body; // Amount should be passed from frontend based on tier logic
  const user = req.user;

  if (!email || !amount) {
    return res.status(400).json({ error: 'Email and amount are required.' });
  }

  try {
    const paystackResponse = await axios.post(
      'https://api.paystack.co/transaction/initialize',
      {
        email: email,
        amount: amount, // amount in kobo
        callback_url: `${process.env.FRONTEND_URL}/dashboard`, // Redirect after payment
        metadata: {
          user_id: user.id,
          // Add any other relevant metadata
        },
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    res.status(200).json(paystackResponse.data);
  } catch (err) {
    console.error('Error initiating Paystack transaction:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to initiate payment.', details: err.response?.data || err.message });
  }
});

// Paystack Webhook Endpoint
app.post('/api/paystack/webhook', async (req, res) => {
  const secret = process.env.PAYSTACK_SECRET_KEY;
  const hash = req.headers['x-paystack-signature'];

  if (!hash) {
    return res.status(400).send('No signature header');
  }

  const crypto = require('crypto');
  const expectedHash = crypto.createHmac('sha512', secret).update(JSON.stringify(req.body)).digest('hex');

  if (expectedHash !== hash) {
    return res.status(400).send('Invalid signature');
  }

  const event = req.body;

  if (event.event === 'charge.success') {
    const { reference, metadata, amount } = event.data;
    const userId = metadata.user_id;
    const paidAmount = amount / 100; // Convert kobo to USD

    try {
      // Determine the new tier based on the paid amount
      let newTier = 'TIER_1';
      if (paidAmount >= (PRICING_TIERS.TIER_2.price / 100)) {
        newTier = 'TIER_2';
      }

      // Update user's payment_status and tier in Supabase
      const { data, error } = await supabase
        .from('profiles')
        .update({ payment_status: 'paid', tier: newTier })
        .eq('id', userId);

      if (error) {
        console.error('Error updating user profile after successful payment:', error);
        return res.status(500).json({ error: 'Failed to update user profile.' });
      }

      console.log(`Payment successful for user ${userId}. Reference: ${reference}. New Tier: ${newTier}`);
      res.status(200).send('Webhook received and processed.');

    } catch (err) {
      console.error('Error processing Paystack webhook:', err.message);
      res.status(500).json({ error: 'Failed to process webhook.' });
    }
  } else {
    // Handle other Paystack events if necessary
    res.status(200).send('Event received, but not processed.');
  }
});

// Get User Receipts Endpoint
app.get('/api/receipts', protect, async (req, res) => {
  try {
    // Get receipts for the user from the receipt_data table
    const { data: receipts, error } = await supabase
      .from('receipt_data')
      .select('*')
      .eq('user_id', req.user.id)
      .order('date', { ascending: false });

    if (error) {
      console.error('Error fetching receipts:', error);
      return res.status(500).json({ error: 'Failed to fetch receipts.', details: error.message });
    }

    res.status(200).json(receipts || []);
  } catch (err) {
    console.error('Error in /api/receipts endpoint:', err);
    res.status(500).json({ error: 'Failed to fetch receipts.', details: err.message });
  }
});

// Error logging middleware
app.use((err, req, res, next) => {
  console.error('Express error:', err);
  res.status(500).json({ error: 'Internal server error', details: err.message, stack: process.env.NODE_ENV === 'development' ? err.stack : undefined });
});

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
  console.error('ERROR: SUPABASE_URL or SUPABASE_ANON_KEY is missing. Please check your .env file in the project root.');
  process.exit(1);
}

app.listen(PORT, () => {
  console.log(`RECO AI backend listening on port ${PORT}`);
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    const newPort = PORT + 1;
    console.log(`Port ${PORT} is busy, trying port ${newPort} instead...`);
    app.listen(newPort, () => {
      console.log(`RECO AI backend listening on port ${newPort}`);
    });
  } else {
    console.error('Failed to start server:', err);
  }
});
