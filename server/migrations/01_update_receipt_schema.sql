-- Add new columns to the Receipt table
ALTER TABLE "Receipt" 
  ADD COLUMN IF NOT EXISTS "vendor_tax_id" TEXT,
  ADD COLUMN IF NOT EXISTS "subtotal" NUMERIC,
  ADD COLUMN IF NOT EXISTS "tax_rate_percent" NUMERIC,
  ADD COLUMN IF NOT EXISTS "tax_amount" NUMERIC,
  ADD COLUMN IF NOT EXISTS "payment_method" TEXT;

-- Update items JSONB structure
-- This is a safe migration that preserves existing data
-- It will rename 'name' to 'description' in items array elements if they exist
UPDATE "Receipt"
SET "items" = (
  SELECT jsonb_agg(
    jsonb_build_object(
      'description', COALESCE(item->>'name', item->>'description', 'Unspecified Item'),
      'total', COALESCE((item->>'price')::numeric, (item->>'total')::numeric, 0),
      'category', COALESCE(item->>'category', 'Uncategorized')
    )
  )
  FROM jsonb_array_elements(COALESCE("items", '[]'::jsonb)) AS item
)
WHERE "items" IS NOT NULL AND jsonb_array_length("items") > 0;

-- For receipts with no items, create a default item based on total_amount
UPDATE "Receipt"
SET "items" = jsonb_build_array(
  jsonb_build_object(
    'description', 'Unspecified Item',
    'total', COALESCE("amount", 0),
    'category', 'Uncategorized'
  )
)
WHERE "items" IS NULL OR jsonb_array_length("items") = 0;

-- Set default values for new columns
UPDATE "Receipt"
SET 
  "payment_method" = 'Cash',
  "subtotal" = COALESCE("amount", 0),
  "tax_rate_percent" = 0,
  "tax_amount" = 0,
  "total_amount" = COALESCE("amount", 0)
WHERE "payment_method" IS NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_receipt_vendor ON "Receipt" ("vendor");
CREATE INDEX IF NOT EXISTS idx_receipt_date ON "Receipt" ("date");
CREATE INDEX IF NOT EXISTS idx_receipt_user_id ON "Receipt" ("user_id");