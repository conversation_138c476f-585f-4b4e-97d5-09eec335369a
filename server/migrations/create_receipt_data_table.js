const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createReceiptDataTable() {
  try {
    // Create the receipt_data table with the new schema
    const { error } = await supabase.rpc('create_receipt_data_table', {
      sql: `
        -- First, ensure the uuid-ossp extension is enabled
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        
        -- Drop the table if it exists to recreate with correct schema
        DROP TABLE IF EXISTS receipt_data;
        
        CREATE TABLE receipt_data (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          vendor TEXT,
          vendor_tax_id TEXT,
          date DATE,
          category TEXT,
          payment_method TEXT,
          currency TEXT DEFAULT 'USD',
          subtotal DECIMAL(10, 2),
          tax_rate_percent DECIMAL(5, 2),
          tax_amount DECIMAL(10, 2),
          total_amount DECIMAL(10, 2) NOT NULL,
          items JSONB,
          source_file TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create index on user_id for faster queries
        CREATE INDEX receipt_data_user_id_idx ON receipt_data(user_id);
        
        -- Create index on date for sorting
        CREATE INDEX receipt_data_date_idx ON receipt_data(date);
        
        -- Enable RLS
        ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for users to see only their own receipts
        CREATE POLICY "Users can view their own receipts"
          ON receipt_data
          FOR SELECT
          USING (auth.uid() = user_id);
        
        -- Create policy for users to insert their own receipts
        CREATE POLICY "Users can insert their own receipts"
          ON receipt_data
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
        
        -- Create policy for users to update their own receipts
        CREATE POLICY "Users can update their own receipts"
          ON receipt_data
          FOR UPDATE
          USING (auth.uid() = user_id);
        
        -- Create policy for users to delete their own receipts
        CREATE POLICY "Users can delete their own receipts"
          ON receipt_data
          FOR DELETE
          USING (auth.uid() = user_id);
      `
    });

    if (error) {
      console.error('Error creating receipt_data table:', error);
      return;
    }

    console.log('receipt_data table created successfully');
  } catch (error) {
    console.error('Error in migration script:', error);
  }
}

createReceiptDataTable();
