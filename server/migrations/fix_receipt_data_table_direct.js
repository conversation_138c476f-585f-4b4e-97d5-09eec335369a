const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');

// Load environment variables from the root .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Log environment variable status (without revealing values)
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Loaded' : 'Not Loaded');

// Check if required environment variables are available
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('ERROR: Required environment variables are missing.');
  console.error('Make sure your .env file contains SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// SQL statements to fix the receipt_data table
const sqlStatements = [
  // Enable uuid-ossp extension
  `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
  
  // Drop the table if it exists
  `DROP TABLE IF EXISTS receipt_data;`,
  
  // Create the table with the correct structure
  `CREATE TABLE IF NOT EXISTS receipt_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vendor TEXT,
    vendor_tax_id TEXT,
    date DATE,
    category TEXT,
    payment_method TEXT,
    currency TEXT DEFAULT 'USD',
    subtotal DECIMAL(10, 2),
    tax_rate_percent DECIMAL(5, 2),
    tax_amount DECIMAL(10, 2),
    total_amount DECIMAL(10, 2) NOT NULL,
    items JSONB,
    source_file TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );`,
  
  // Create indexes
  `CREATE INDEX IF NOT EXISTS receipt_data_user_id_idx ON receipt_data(user_id);`,
  `CREATE INDEX IF NOT EXISTS receipt_data_date_idx ON receipt_data(date);`,
  
  // Enable RLS
  `ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;`,
  
  // Create policies
  `CREATE POLICY IF NOT EXISTS "Users can view their own receipts"
    ON receipt_data
    FOR SELECT
    USING (auth.uid() = user_id);`,
    
  `CREATE POLICY IF NOT EXISTS "Users can insert their own receipts"
    ON receipt_data
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);`,
    
  `CREATE POLICY IF NOT EXISTS "Users can update their own receipts"
    ON receipt_data
    FOR UPDATE
    USING (auth.uid() = user_id);`,
    
  `CREATE POLICY IF NOT EXISTS "Users can delete their own receipts"
    ON receipt_data
    FOR DELETE
    USING (auth.uid() = user_id);`
];

async function executeSQL(sql) {
  try {
    console.log(`Executing SQL: ${sql.substring(0, 50)}...`);
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return false;
    }
    
    console.log('SQL executed successfully');
    return true;
  } catch (err) {
    console.error('Error in executeSQL:', err);
    return false;
  }
}

async function fixReceiptDataTable() {
  try {
    console.log('Attempting to fix receipt_data table...');
    
    // Execute each SQL statement
    for (const sql of sqlStatements) {
      const success = await executeSQL(sql);
      if (!success) {
        console.error('Failed to execute SQL statement. Stopping execution.');
        return;
      }
    }
    
    console.log('receipt_data table fixed successfully');
  } catch (error) {
    console.error('Error in fix script:', error);
  }
}

// Run the fix function
fixReceiptDataTable()
  .then(() => {
    console.log('Fix script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Fix script failed:', err);
    process.exit(1);
  });