const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');

// Try to load environment variables from multiple possible locations
const envPaths = [
  path.resolve(__dirname, '.env'),                // Local .env in migrations directory
  path.resolve(__dirname, '../../.env'),          // Root project .env
  path.resolve(__dirname, '../.env'),             // Server directory .env
];

let envLoaded = false;

for (const envPath of envPaths) {
  if (fs.existsSync(envPath)) {
    console.log(`Loading environment from: ${envPath}`);
    dotenv.config({ path: envPath });
    envLoaded = true;
    break;
  }
}

if (!envLoaded) {
  console.warn('No .env file found in any of the expected locations.');
}

// Hardcoded fallback values (only if not found in environment)
if (!process.env.SUPABASE_URL) {
  process.env.SUPABASE_URL = 'https://kvaxohpaowosqnprcfwq.supabase.co';
  console.log('Using hardcoded SUPABASE_URL as fallback');
}

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2YXhvaHBhb3dvc3FucHJjZndxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM2MzUzMiwiZXhwIjoyMDYzOTM5NTMyfQ.SXu-v0jt3VKawCXNQn06pp8a7mFyz46_1YYlkhjV1wY';
  console.log('Using hardcoded SUPABASE_SERVICE_ROLE_KEY as fallback');
}

// Log environment variable status (without revealing values)
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Loaded' : 'Not Loaded');

// Check if required environment variables are available
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('ERROR: Required environment variables are missing.');
  console.error('Make sure your .env file contains SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixReceiptDataTable() {
  try {
    console.log('Attempting to fix receipt_data table...');
    
    // Run SQL to fix the table structure
    const { error } = await supabase.rpc('run_sql', {
      sql: `
        -- First, ensure the uuid-ossp extension is enabled
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        
        -- Check if the table exists
        DO $$
        BEGIN
          IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'receipt_data') THEN
            -- Drop the table if it has the wrong structure
            DROP TABLE receipt_data;
          END IF;
        END
        $$;
        
        -- Create the table with the correct structure
        CREATE TABLE IF NOT EXISTS receipt_data (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          vendor TEXT,
          vendor_tax_id TEXT,
          date DATE,
          category TEXT,
          payment_method TEXT,
          currency TEXT DEFAULT 'USD',
          subtotal DECIMAL(10, 2),
          tax_rate_percent DECIMAL(5, 2),
          tax_amount DECIMAL(10, 2),
          total_amount DECIMAL(10, 2) NOT NULL,
          items JSONB,
          source_file TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create index on user_id for faster queries
        CREATE INDEX IF NOT EXISTS receipt_data_user_id_idx ON receipt_data(user_id);
        
        -- Create index on date for sorting
        CREATE INDEX IF NOT EXISTS receipt_data_date_idx ON receipt_data(date);
        
        -- Enable RLS
        ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for users to see only their own receipts
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'receipt_data' AND policyname = 'Users can view their own receipts'
          ) THEN
            CREATE POLICY "Users can view their own receipts"
              ON receipt_data
              FOR SELECT
              USING (auth.uid() = user_id);
          END IF;
        END
        $$;
        
        -- Create policy for users to insert their own receipts
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'receipt_data' AND policyname = 'Users can insert their own receipts'
          ) THEN
            CREATE POLICY "Users can insert their own receipts"
              ON receipt_data
              FOR INSERT
              WITH CHECK (auth.uid() = user_id);
          END IF;
        END
        $$;
        
        -- Create policy for users to update their own receipts
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'receipt_data' AND policyname = 'Users can update their own receipts'
          ) THEN
            CREATE POLICY "Users can update their own receipts"
              ON receipt_data
              FOR UPDATE
              USING (auth.uid() = user_id);
          END IF;
        END
        $$;
        
        -- Create policy for users to delete their own receipts
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'receipt_data' AND policyname = 'Users can delete their own receipts'
          ) THEN
            CREATE POLICY "Users can delete their own receipts"
              ON receipt_data
              FOR DELETE
              USING (auth.uid() = user_id);
          END IF;
        END
        $$;
      `
    });

    if (error) {
      console.error('Error fixing receipt_data table:', error);
      return;
    }

    console.log('receipt_data table fixed successfully');
  } catch (error) {
    console.error('Error in fix script:', error);
  }
}

// Run the fix function
fixReceiptDataTable()
  .then(() => {
    console.log('Fix script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Fix script failed:', err);
    process.exit(1);
  });
