const { createClient } = require('@supabase/supabase-js');

// Hardcoded values for simplicity
const supabaseUrl = 'https://kvaxohpaowosqnprcfwq.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2YXhvaHBhb3dvc3FucHJjZndxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM2MzUzMiwiZXhwIjoyMDYzOTM5NTMyfQ.SXu-v0jt3VKawCXNQn06pp8a7mFyz46_1YYlkhjV1wY';

console.log('Using hardcoded credentials');

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixReceiptDataTable() {
  try {
    console.log('Attempting to fix receipt_data table...');
    
    // SQL to fix the table structure
    const sql = `
      -- First, ensure the uuid-ossp extension is enabled
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      
      -- Check if the table exists and drop it
      DROP TABLE IF EXISTS receipt_data;
      
      -- Create the table with the correct structure
      CREATE TABLE receipt_data (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
        vendor TEXT,
        vendor_tax_id TEXT,
        date DATE,
        category TEXT,
        payment_method TEXT,
        currency TEXT DEFAULT 'USD',
        subtotal DECIMAL(10, 2),
        tax_rate_percent DECIMAL(5, 2),
        tax_amount DECIMAL(10, 2),
        total_amount DECIMAL(10, 2) NOT NULL,
        items JSONB,
        source_file TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Create index on user_id for faster queries
      CREATE INDEX receipt_data_user_id_idx ON receipt_data(user_id);
      
      -- Create index on date for sorting
      CREATE INDEX receipt_data_date_idx ON receipt_data(date);
      
      -- Enable RLS
      ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;
      
      -- Create policies
      CREATE POLICY "Users can view their own receipts"
        ON receipt_data
        FOR SELECT
        USING (auth.uid() = user_id);
        
      CREATE POLICY "Users can insert their own receipts"
        ON receipt_data
        FOR INSERT
        WITH CHECK (auth.uid() = user_id);
        
      CREATE POLICY "Users can update their own receipts"
        ON receipt_data
        FOR UPDATE
        USING (auth.uid() = user_id);
        
      CREATE POLICY "Users can delete their own receipts"
        ON receipt_data
        FOR DELETE
        USING (auth.uid() = user_id);
    `;
    
    // Execute the SQL
    const { error } = await supabase.rpc('run_sql', { sql });

    if (error) {
      console.error('Error fixing receipt_data table:', error);
      return;
    }

    console.log('receipt_data table fixed successfully');
  } catch (error) {
    console.error('Error in fix script:', error);
  }
}

// Run the fix function
fixReceiptDataTable()
  .then(() => {
    console.log('Fix script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Fix script failed:', err);
    process.exit(1);
  });