-- Enable uuid-ossp extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop the table if it exists
DROP TABLE IF EXISTS receipt_data;

-- Create the table with the correct structure
CREATE TABLE receipt_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  vendor TEXT,
  vendor_tax_id TEXT,
  date DATE,
  category TEXT,
  payment_method TEXT,
  currency TEXT DEFAULT 'USD',
  subtotal DECIMAL(10, 2),
  tax_rate_percent DECIMAL(5, 2),
  tax_amount DECIMAL(10, 2),
  total_amount DECIMAL(10, 2) NOT NULL,
  items JSONB,
  source_file TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS receipt_data_user_id_idx ON receipt_data(user_id);
CREATE INDEX IF NOT EXISTS receipt_data_date_idx ON receipt_data(date);

-- Enable RLS
ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own receipts"
  ON receipt_data
  FOR SELECT
  USING (auth.uid() = user_id);
  
CREATE POLICY "Users can insert their own receipts"
  ON receipt_data
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can update their own receipts"
  ON receipt_data
  FOR UPDATE
  USING (auth.uid() = user_id);
  
CREATE POLICY "Users can delete their own receipts"
  ON receipt_data
  FOR DELETE
  USING (auth.uid() = user_id);