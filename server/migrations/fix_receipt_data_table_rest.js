const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');
const { setTimeout } = require('timers');

// Try to load environment variables from multiple possible locations
const envPaths = [
  path.resolve(__dirname, '.env'),                // Local .env in migrations directory
  path.resolve(__dirname, '../../.env'),          // Root project .env
  path.resolve(__dirname, '../.env'),             // Server directory .env
];

let envLoaded = false;

for (const envPath of envPaths) {
  if (fs.existsSync(envPath)) {
    console.log(`Loading environment from: ${envPath}`);
    dotenv.config({ path: envPath });
    envLoaded = true;
    break;
  }
}

if (!envLoaded) {
  console.warn('No .env file found in any of the expected locations.');
}

// Hardcoded fallback values (only if not found in environment)
if (!process.env.SUPABASE_URL) {
  process.env.SUPABASE_URL = 'https://kvaxohpaowosqnprcfwq.supabase.co';
  console.log('Using hardcoded SUPABASE_URL as fallback');
}

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2YXhvaHBhb3dvc3FucHJjZndxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM2MzUzMiwiZXhwIjoyMDYzOTM5NTMyfQ.SXu-v0jt3VKawCXNQn06pp8a7mFyz46_1YYlkhjV1wY';
  console.log('Using hardcoded SUPABASE_SERVICE_ROLE_KEY as fallback');
}

// Log environment variable status (without revealing values)
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Loaded' : 'Not Loaded');

// Create Supabase client with service role key
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// SQL statements to fix the receipt_data table
const sqlStatements = [
  // Enable uuid-ossp extension
  `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
  
  // Drop the table if it exists
  `DROP TABLE IF EXISTS receipt_data;`,
  
  // Create the table with the correct structure
  `CREATE TABLE receipt_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vendor TEXT,
    vendor_tax_id TEXT,
    date DATE,
    category TEXT,
    payment_method TEXT,
    currency TEXT DEFAULT 'USD',
    subtotal DECIMAL(10, 2),
    tax_rate_percent DECIMAL(5, 2),
    tax_amount DECIMAL(10, 2),
    total_amount DECIMAL(10, 2) NOT NULL,
    items JSONB,
    source_file TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );`,
  
  // Create indexes
  `CREATE INDEX IF NOT EXISTS receipt_data_user_id_idx ON receipt_data(user_id);`,
  `CREATE INDEX IF NOT EXISTS receipt_data_date_idx ON receipt_data(date);`,
  
  // Enable RLS
  `ALTER TABLE receipt_data ENABLE ROW LEVEL SECURITY;`,
  
  // Create policies
  `CREATE POLICY "Users can view their own receipts"
    ON receipt_data
    FOR SELECT
    USING (auth.uid() = user_id);`,
    
  `CREATE POLICY "Users can insert their own receipts"
    ON receipt_data
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);`,
    
  `CREATE POLICY "Users can update their own receipts"
    ON receipt_data
    FOR UPDATE
    USING (auth.uid() = user_id);`,
    
  `CREATE POLICY "Users can delete their own receipts"
    ON receipt_data
    FOR DELETE
    USING (auth.uid() = user_id);`
];

async function fixReceiptDataTable() {
  try {
    console.log('Attempting to fix receipt_data table...');
    
    // Execute each SQL statement directly using the REST API
    for (const sql of sqlStatements) {
      console.log(`Executing SQL: ${sql.substring(0, 50)}...`);
      
      // Use the REST API to execute SQL
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      try {
        const response = await fetch(`${process.env.SUPABASE_URL}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
            'Prefer': 'params=single-object',
            'X-Client-Info': 'supabase-js/2.x'
          },
          body: JSON.stringify({
            query: sql
          }),
          signal: controller.signal // Apply the timeout signal
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Error executing SQL:', errorData);
          console.error('Failed to execute SQL statement. Stopping execution.');
          return;
        }
        
        console.log('SQL executed successfully');
      } finally {
        clearTimeout(timeoutId); // Clear the timeout regardless of success or failure
      }
    }
    
    console.log('receipt_data table fixed successfully');
  } catch (error) {
    console.error('Error in fix script:', error);
  }
}

// Run the fix function
fixReceiptDataTable()
  .then(() => {
    console.log('Fix script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Fix script failed:', err);
    process.exit(1);
  });
