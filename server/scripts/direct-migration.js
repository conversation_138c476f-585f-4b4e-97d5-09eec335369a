const fs = require('fs');
const path = require('path');
const https = require('https');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

// Verify environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('ERROR: Required environment variables are missing.');
  console.error('Make sure your .env file contains SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Path to the migration file
const migrationFile = path.join(__dirname, '..', 'migrations', '01_update_receipt_schema.sql');

// Function to execute SQL via REST API
function executeSql(sql) {
  return new Promise((resolve, reject) => {
    // Parse the Supabase URL to get the host
    const url = new URL(process.env.SUPABASE_URL);
    const host = url.hostname;
    
    // Prepare the request options
    const options = {
      hostname: host,
      path: '/rest/v1/rpc/execute_sql',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      }
    };
    
    // Create the request
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(data);
        } else {
          reject(new Error(`HTTP Error: ${res.statusCode} - ${data}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    // Send the request with the SQL payload
    req.write(JSON.stringify({ sql_query: sql }));
    req.end();
  });
}

// Function to run the migration
async function runMigration() {
  try {
    console.log('Starting migration process...');
    
    // Check if the migration file exists
    if (!fs.existsSync(migrationFile)) {
      console.error(`Migration file not found: ${migrationFile}`);
      return;
    }
    
    // Read the migration file
    const sql = fs.readFileSync(migrationFile, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      console.log(`Executing SQL: ${statement.substring(0, 50)}...`);
      try {
        await executeSql(statement);
        console.log('Statement executed successfully.');
      } catch (error) {
        console.error('Error executing statement:', error.message);
      }
    }
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error.message);
  }
}

runMigration();