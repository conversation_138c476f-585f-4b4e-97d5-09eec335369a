const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Path to the migration file
const migrationFile = path.join(__dirname, '..', 'migrations', '01_update_receipt_schema.sql');

// Function to run the migration using Supabase CLI
function runMigration() {
  try {
    console.log('Starting migration process...');
    
    // Check if the migration file exists
    if (!fs.existsSync(migrationFile)) {
      console.error(`Migration file not found: ${migrationFile}`);
      return;
    }
    
    // Create a temporary migration file for Supabase CLI
    const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').substring(0, 14);
    const tempMigrationDir = path.join(__dirname, '..', 'supabase', 'migrations');
    
    // Create the directory if it doesn't exist
    if (!fs.existsSync(tempMigrationDir)) {
      fs.mkdirSync(tempMigrationDir, { recursive: true });
    }
    
    const tempMigrationFile = path.join(tempMigrationDir, `${timestamp}_receipt_schema_update.sql`);
    
    // Copy the migration content
    fs.copyFileSync(migrationFile, tempMigrationFile);
    console.log(`Created temporary migration file: ${tempMigrationFile}`);
    
    // Run the migration using Supabase CLI
    console.log('Running migration with Supabase CLI...');
    const result = execSync('supabase db push', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    });
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error.message);
  }
}

runMigration();