const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables from the local .env file
dotenv.config({ path: path.join(__dirname, '.env') });

// Debug: Log environment variable status
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Loaded' : 'Not Loaded');

// Verify environment variables are available
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('ERROR: Required environment variables are missing.');
  console.error('Make sure your .env file contains SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function runMigrations() {
  console.log('Starting database migrations...');
  
  try {
    // Create migrations table if it doesn't exist
    await createMigrationsTable();
    
    // Get all SQL files from the migrations directory
    const migrationsDir = path.join(__dirname, '..', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure migrations run in order
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found.');
      return;
    }
    
    // Get already executed migrations
    const { data: executedMigrations, error: fetchError } = await supabaseAdmin
      .from('migrations')
      .select('name');
    
    if (fetchError) {
      console.error('Error fetching executed migrations:', fetchError);
      return;
    }
    
    const executedMigrationNames = executedMigrations ? executedMigrations.map(m => m.name) : [];
    
    // Run each migration that hasn't been executed yet
    for (const file of migrationFiles) {
      if (executedMigrationNames.includes(file)) {
        console.log(`Migration ${file} already executed, skipping...`);
        continue;
      }
      
      console.log(`Running migration: ${file}`);
      const migrationPath = path.join(migrationsDir, file);
      const sql = fs.readFileSync(migrationPath, 'utf8');
      
      try {
        // Execute the SQL migration directly using query
        const { error } = await supabaseAdmin.rpc('execute_sql', { sql_query: sql });
        
        if (error) {
          // If the RPC fails, try executing the SQL statements one by one
          console.log('RPC failed, trying direct SQL execution...');
          await executeSqlDirectly(sql);
        }
        
        // Record the migration as executed
        const { error: insertError } = await supabaseAdmin
          .from('migrations')
          .insert({ name: file, executed_at: new Date().toISOString() });
        
        if (insertError) {
          console.error(`Error recording migration ${file}:`, insertError);
          return;
        }
        
        console.log(`Migration ${file} executed successfully.`);
      } catch (err) {
        console.error(`Error in migration ${file}:`, err);
        return;
      }
    }
    
    console.log('All migrations completed successfully.');
  } catch (err) {
    console.error('Error running migrations:', err);
  }
}

// Create migrations table directly
async function createMigrationsTable() {
  console.log('Creating migrations table if it doesn\'t exist...');
  
  const createTableSql = `
  CREATE TABLE IF NOT EXISTS migrations (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    executed_at TIMESTAMP WITH TIME ZONE NOT NULL
  );
  `;
  
  try {
    // Try to execute the SQL directly using query
    const { error } = await supabaseAdmin.rpc('execute_sql', { sql_query: createTableSql });
    
    if (error) {
      console.log('RPC failed, trying direct SQL execution...');
      await executeSqlDirectly(createTableSql);
    }
    
    console.log('Migrations table created or already exists.');
  } catch (err) {
    console.error('Error creating migrations table:', err);
    throw err;
  }
}

// Create a function to execute SQL directly
async function executeSqlDirectly(sql) {
  // Split the SQL into individual statements
  const statements = sql.split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  // Execute each statement separately
  for (const statement of statements) {
    try {
      const { data, error } = await supabaseAdmin
        .from('_sql')
        .select('*')
        .eq('query', statement);
      
      if (error) {
        console.error('Error executing SQL statement:', error);
        throw error;
      }
    } catch (err) {
      console.error('Error in executeSqlDirectly:', err);
      throw err;
    }
  }
}

// Create a stored procedure for executing SQL
async function createExecuteSqlFunction() {
  console.log('Creating execute_sql function...');
  
  const createFunctionSql = `
  CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
  RETURNS void AS $$
  BEGIN
    EXECUTE sql_query;
  END;
  $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;
  
  try {
    // Try to create the function using a direct approach
    const { data, error } = await supabaseAdmin
      .from('_sql')
      .select('*')
      .eq('query', createFunctionSql);
    
    if (error) {
      console.error('Error creating execute_sql function:', error);
      throw error;
    }
    
    console.log('Execute SQL function created successfully.');
  } catch (err) {
    console.error('Error in createExecuteSqlFunction:', err);
    throw err;
  }
}

// Run the migration process
async function main() {
  try {
    await createExecuteSqlFunction();
    await runMigrations();
  } catch (err) {
    console.error('Migration error:', err);
  }
}

main();
