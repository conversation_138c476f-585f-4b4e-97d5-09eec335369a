1. Project Overview
•	Stack: React (frontend), Node.js (backend), Supabase (auth & DB), OpenAI (best model for receipt extraction), Google Sheets API (per-user export), Paystack (payments).
•	Core Functionality: Users upload/scan receipts, extract structured data, export to Google Sheets (per user), and access insights (future).
•	User Roles: Accountant, Freelancer, Business Owner.
________________________________________
2. User Flow & Features
2.1. Authentication & Onboarding
•	Sign In: Email/password or Google OAuth via Supabase.
•	Onboarding Steps:
1.	User signs in.
2.	Chooses a role (Accountant, Freelancer, Business Owner).
3.	Uploads or connects a Google Drive folder for receipts.
4.	Scans 1 receipt as a demo (upload or select from Drive).
5.	Sees extracted data in a preview table (fields as per example).
6.	Option to download as Google Sheet (per user).
2.2. Receipt Processing
•	Upload/Import: User uploads image or connects Google Drive.
•	Extraction: Use the best OpenAI model for image-to-structured-data extraction.
•	Storage: Store extracted data per user in Supabase.
•	Export: Allow user to export/download all their receipts as a Google Sheet.
2.3. Pricing Tiers & Payments
•	Tier 1: <200 receipts = $50
•	Tier 2: >200 receipts = $100
•	UI: Show current tier, receipt count, and upgrade prompt if needed.
•	Payment Integration: Use Paystack for payment processing (API per https://paystack.com/docs/api/transaction/).
•	Enforcement: Restrict access to features based on payment/tier.
2.4. Future Features (Not MVP)
•	AI agent to fetch receipts from email.
•	AI-generated insights and reports from Google Sheet data.
________________________________________
3. Technical Plan
3.1. Frontend (React)
•	Pages/Components:
•	Auth (Sign In/Up, Google OAuth)
•	Onboarding Wizard (role selection, upload/connect, scan demo)
•	Receipt Upload/Import
•	Receipt Table/Preview
•	Export to Google Sheets
•	Pricing & Usage Dashboard
•	Paystack Payment Modal/Flow
•	UI/UX Design:
•	Color Palette:
•	Primary: Deep charcoal (#0a0a0a) to navy (#1a1a2e) gradients
•	Accent: Cyan (#00d4ff), Magenta (#ff0080)
•	Success: Green (#10b981)
•	Warning: Orange (#f59e0b)
•	Error: Red (#ef4444)
•	Glass Morphism Effects:
•	Background: backdrop-filter: blur(20px)
•	Border: 1px solid rgba(255,255,255,0.2)
•	Glow: box-shadow: 0 0 20px rgba(accent-color, 0.3)
•	Typography: Inter/SF Pro, space-graded sizing, max 3 weights per screen
•	Animations: 200-300ms spring, physics-based, reduced motion respect
•	Mobile: Touch-first, min 44px touch targets, gesture-friendly nav
•	Accessibility: WCAG 2.1 AA, keyboard nav, screen reader, 4.5:1 contrast
3.2. Backend (Node.js)
•	APIs:
•	Auth (handled by Supabase)
•	Receipt Upload/Import endpoint
•	OpenAI integration for extraction (choose best model, e.g., GPT-4 Vision or similar)
•	Google Sheets export endpoint (per user)
•	Paystack payment verification and webhook handling
•	Pricing logic (calculate tier, enforce limits)
•	Database: Supabase tables for users, receipts, roles, usage, payment status.
3.3. Integrations
•	Supabase: Auth, user/receipt storage.
•	OpenAI: Best model for receipt extraction.
•	Google Drive: File picker/connection for receipt import.
•	Google Sheets: Per-user export.
•	Paystack: Payment processing and verification.
________________________________________
4. Example Data Model (Supabase)
•	User: id, email, role, tier, receipt_count, payment_status, etc.
•	Receipt: id, user_id, vendor, amount, currency, date, category, payment_method, items, tags, source_file, created_at
________________________________________
5. MVP Milestones
1.	Project Setup: Repo, dependencies, Supabase, Google/Paystack APIs.
2.	Auth & Onboarding: Email/Google sign-in, role selection, onboarding wizard.
3.	Receipt Upload & Extraction: Upload, OpenAI extraction, preview.
4.	Google Drive Integration: Connect/import receipts.
5.	Google Sheets Export: Per-user export/download.
6.	Pricing Logic & Paystack: Tier calculation, UI, payment flow, enforcement.
7.	Testing & Polish: End-to-end test, bugfixes, UI/UX improvements, accessibility.
________________________________________
6. Assumptions & Questions
•	OpenAI Model: Will use the latest and most accurate model for receipt extraction (e.g., GPT-4 Vision or equivalent).
•	Google Sheets: Each user gets their own export; no shared sheets.
•	UI/UX: Will follow your color palette, glass morphism, typography, animation, mobile, and accessibility guidelines.
•	Payments: Paystack will be the only payment processor for MVP.
•	Branding: No additional branding requirements unless specified.
