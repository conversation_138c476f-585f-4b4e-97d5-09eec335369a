# RECO AI

Smart receipt extraction and insights platform for accountants, freelancers, and business owners.

## Project Structure
- `/client` — React PWA frontend
- `/server` — Node.js/Express backend

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repo-url>
cd reco-ai
```

### 2. Environment Variables
- Copy `.env.example` to `.env` in both `/client` and `/server`.
- Fill in the required values (see `.env.example` for each project).

### 3. Install Dependencies
```bash
cd client && npm install
cd ../server && npm install
```

### 4. Running the App
#### Backend
```bash
cd server
npm start
```

#### Frontend
```bash
cd client
npm start
```

### 5. Health Check
- Backend: [http://localhost:5000/api/health](http://localhost:5000/api/health)
- Frontend: [http://localhost:3000](http://localhost:3000)

## Tech Stack
- React, Node.js/Express, Supabase, OpenAI, Paystack, Google APIs

## Supabase Project
- Project ID: kvaxohpaowosqnprcfwq

## License
MIT 