import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { supabase } from './supabaseClient';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import CheckEmail from './pages/CheckEmail';
import LandingPage from './pages/LandingPage';
import './App.css';

function App() {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p style={{ marginTop: 16, color: 'white' }}>Loading RECO AI...</p>
      </div>
    );
  }

  console.log('App rendering - Session:', session ? 'exists' : 'none');

  return (
    <div>
      {/* Debug banner to confirm updates are working */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: '#00D4FF',
        color: '#000',
        padding: '12px',
        textAlign: 'center',
        zIndex: 9999,
        fontSize: '16px',
        fontWeight: 'bold',
        boxShadow: '0 2px 10px rgba(0,212,255,0.5)'
      }}>
        ✨ RECO AI - MODERN UI IS LIVE! ✨ - Time: {new Date().toLocaleTimeString()} - Session: {session ? 'Logged In' : 'Not Logged In'}
      </div>
      <div style={{ paddingTop: '40px' }}>
        <Router>
          <Routes>
            <Route path="/" element={session ? <Navigate to="/dashboard" /> : <LandingPage />} />
            <Route path="/login" element={session ? <Navigate to="/dashboard" /> : <Login />} />
            <Route path="/check-email" element={<CheckEmail />} />
            <Route path="/dashboard" element={session ? <Dashboard /> : <Navigate to="/login" />} />
            <Route path="*" element={<Navigate to="/" />} />
          </Routes>
        </Router>
      </div>
    </div>
  );
}

export default App;
