import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';

export default function Onboarding() {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
    fullName: '',
    companyName: '',
    role: '',
    useCase: '',
  });
  const navigate = useNavigate();

  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      if (data?.user) {
        setUser(data.user);
      } else {
        navigate('/login');
      }
    };
    
    getUser();
  }, [navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      if (!formData.fullName) {
        setError('Please enter your name');
        return;
      }
    }
    
    setError('');
    setCurrentStep(prev => prev + 1);
  };

  const handlePrevStep = () => {
    setError('');
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!formData.useCase) {
      setError('Please select how you plan to use RECO AI');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Update user profile in Supabase
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: formData.fullName,
          company_name: formData.companyName,
          role: formData.role,
          use_case: formData.useCase,
          onboarding_completed: true,
        });
      
      if (updateError) throw updateError;
      
      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to complete onboarding');
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div>
            <h2 style={{ color: 'white', marginBottom: 24 }}>Welcome to RECO AI!</h2>
            <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 32 }}>
              Let's get to know you better so we can personalize your experience.
            </p>
            
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', color: 'white', marginBottom: 8 }}>
                What's your name?
              </label>
              <input
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                placeholder="Enter your full name"
                style={{
                  padding: 16,
                  borderRadius: 8,
                  border: '1px solid rgba(255,255,255,0.2)',
                  backgroundColor: 'rgba(0,0,0,0.2)',
                  color: 'white',
                  fontSize: 16,
                  width: '100%',
                }}
              />
            </div>
            
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', color: 'white', marginBottom: 8 }}>
                Company name (optional)
              </label>
              <input
                type="text"
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                placeholder="Enter your company name"
                style={{
                  padding: 16,
                  borderRadius: 8,
                  border: '1px solid rgba(255,255,255,0.2)',
                  backgroundColor: 'rgba(0,0,0,0.2)',
                  color: 'white',
                  fontSize: 16,
                  width: '100%',
                }}
              />
            </div>
          </div>
        );
      
      case 2:
        return (
          <div>
            <h2 style={{ color: 'white', marginBottom: 24 }}>Your Role</h2>
            <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 32 }}>
              This helps us tailor RECO AI to your specific needs.
            </p>
            
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', color: 'white', marginBottom: 16 }}>
                What best describes your role?
              </label>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                {['Accountant/Bookkeeper', 'Business Owner', 'Freelancer/Contractor', 'Finance Professional', 'Other'].map(role => (
                  <label 
                    key={role}
                    style={{ 
                      display: 'flex',
                      alignItems: 'center',
                      padding: 16,
                      borderRadius: 8,
                      backgroundColor: formData.role === role ? 'rgba(0,212,255,0.1)' : 'rgba(0,0,0,0.2)',
                      border: formData.role === role ? `1px solid ${colors.cyan}` : '1px solid rgba(255,255,255,0.2)',
                      cursor: 'pointer',
                    }}
                  >
                    <input
                      type="radio"
                      name="role"
                      value={role}
                      checked={formData.role === role}
                      onChange={handleInputChange}
                      style={{ marginRight: 12 }}
                    />
                    <span style={{ color: 'white' }}>{role}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 3:
        return (
          <div>
            <h2 style={{ color: 'white', marginBottom: 24 }}>How will you use RECO AI?</h2>
            <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 32 }}>
              Select your primary use case so we can optimize your experience.
            </p>
            
            <div style={{ marginBottom: 24 }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                {[
                  'Expense tracking for tax purposes',
                  'Client billing and reimbursements',
                  'Business expense management',
                  'Receipt organization and storage',
                  'Other'
                ].map(useCase => (
                  <label 
                    key={useCase}
                    style={{ 
                      display: 'flex',
                      alignItems: 'center',
                      padding: 16,
                      borderRadius: 8,
                      backgroundColor: formData.useCase === useCase ? 'rgba(0,212,255,0.1)' : 'rgba(0,0,0,0.2)',
                      border: formData.useCase === useCase ? `1px solid ${colors.cyan}` : '1px solid rgba(255,255,255,0.2)',
                      cursor: 'pointer',
                    }}
                  >
                    <input
                      type="radio"
                      name="useCase"
                      value={useCase}
                      checked={formData.useCase === useCase}
                      onChange={handleInputChange}
                      style={{ marginRight: 12 }}
                    />
                    <span style={{ color: 'white' }}>{useCase}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 24,
      background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
    }}>
      <div style={{ 
        maxWidth: 600,
        width: '100%',
        padding: 40,
        borderRadius: 16,
        ...glassMorphism,
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 32 }}>
          {[1, 2, 3].map(step => (
            <div 
              key={step}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: currentStep >= step ? colors.cyan : 'rgba(255,255,255,0.1)',
                color: currentStep >= step ? colors.charcoal : 'rgba(255,255,255,0.5)',
                fontWeight: 'bold',
                position: 'relative',
              }}
            >
              {step}
              {step < 3 && (
                <div style={{
                  position: 'absolute',
                  height: 2,
                  width: 'calc(100% + 40px)',
                  backgroundColor: currentStep > step ? colors.cyan : 'rgba(255,255,255,0.1)',
                  right: -60,
                  top: '50%',
                  transform: 'translateY(-50%)',
                }} />
              )}
            </div>
          ))}
        </div>
        
        {renderStep()}
        
        {error && (
          <div style={{ color: colors.error, marginTop: 16, marginBottom: 16 }}>
            {error}
          </div>
        )}
        
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 32 }}>
          {currentStep > 1 ? (
            <button
              onClick={handlePrevStep}
              style={{
                padding: '12px 24px',
                borderRadius: 8,
                backgroundColor: 'rgba(255,255,255,0.1)',
                color: 'white',
                fontWeight: 500,
                border: 'none',
                cursor: 'pointer',
              }}
            >
              Back
            </button>
          ) : (
            <div></div>
          )}
          
          {currentStep < 3 ? (
            <button
              onClick={handleNextStep}
              style={{
                padding: '12px 24px',
                borderRadius: 8,
                backgroundColor: colors.magenta,
                color: 'white',
                fontWeight: 700,
                border: 'none',
                cursor: 'pointer',
              }}
            >
              Continue
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              style={{
                padding: '12px 24px',
                borderRadius: 8,
                backgroundColor: colors.magenta,
                color: 'white',
                fontWeight: 700,
                border: 'none',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1,
              }}
            >
              {loading ? 'Saving...' : 'Get Started'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
