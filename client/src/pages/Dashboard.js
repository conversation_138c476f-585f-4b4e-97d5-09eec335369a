import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';
import UploadModal from '../components/UploadModal';
import ReceiptTable from '../components/ReceiptTable';

export default function Dashboard() {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [receipts, setReceipts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [stats, setStats] = useState({
    totalReceipts: 0,
    totalAmount: 0,
    thisMonth: 0,
    lastMonth: 0,
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Get current user
        const { data: userData, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error getting user:', userError);
          setLoading(false);
          return;
        }

        if (userData?.user) {
          setUser(userData.user);

          // Try to get user profile (optional - might not exist)
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', userData.user.id)
              .single();

            if (!profileError && profileData) {
              setProfile(profileData);
            }
          } catch (profileError) {
            console.log('Profile not found or error:', profileError);
            // Profile is optional, continue without it
          }

          // Try to get user receipts (optional - might not exist)
          try {
            const { data: receiptsData, error: receiptsError } = await supabase
              .from('receipts')
              .select('*')
              .eq('user_id', userData.user.id)
              .order('created_at', { ascending: false });

            if (!receiptsError && receiptsData) {
              setReceipts(receiptsData || []);
              calculateStats(receiptsData || []);
            } else {
              setReceipts([]);
              calculateStats([]);
            }
          } catch (receiptsError) {
            console.log('Receipts not found or error:', receiptsError);
            // Receipts table might not exist yet, start with empty array
            setReceipts([]);
            calculateStats([]);
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);
  
  const calculateStats = (receiptsData) => {
    // Ensure receiptsData is an array
    const safeReceiptsData = Array.isArray(receiptsData) ? receiptsData : [];
    const total = safeReceiptsData.length;

    // Calculate total amount
    const totalAmount = safeReceiptsData.reduce((sum, receipt) => {
      const amount = receipt?.total_amount || receipt?.amount || 0;
      return sum + (parseFloat(amount) || 0);
    }, 0);

    // Calculate this month's total
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisMonthTotal = safeReceiptsData
      .filter(receipt => {
        if (!receipt?.date) return false;
        return new Date(receipt.date) >= thisMonth;
      })
      .reduce((sum, receipt) => {
        const amount = receipt?.total_amount || receipt?.amount || 0;
        return sum + (parseFloat(amount) || 0);
      }, 0);

    // Calculate last month's total
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    const lastMonthTotal = safeReceiptsData
      .filter(receipt => {
        if (!receipt?.date) return false;
        const date = new Date(receipt.date);
        return date >= lastMonth && date <= lastMonthEnd;
      })
      .reduce((sum, receipt) => {
        const amount = receipt?.total_amount || receipt?.amount || 0;
        return sum + (parseFloat(amount) || 0);
      }, 0);

    setStats({
      totalReceipts: total,
      totalAmount,
      thisMonth: thisMonthTotal,
      lastMonth: lastMonthTotal,
    });
  };
  
  const handleUploadSuccess = (newReceipt) => {
    setReceipts([newReceipt, ...receipts]);
    calculateStats([newReceipt, ...receipts]);
    setShowUploadModal(false);
  };
  
  const handleSignOut = async () => {
    await supabase.auth.signOut();
  };
  
  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
      }}>
        <div style={{ color: 'white', fontSize: 18 }}>Loading...</div>
      </div>
    );
  }

  // If not loading but no user, redirect to login
  if (!loading && !user) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
      }}>
        <div style={{ color: 'white', fontSize: 18, textAlign: 'center' }}>
          <p>Please sign in to access the dashboard.</p>
          <button
            onClick={() => window.location.href = '/'}
            style={{
              padding: '12px 24px',
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              fontWeight: 600,
              marginTop: 16,
            }}
          >
            Go to Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh',
      background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
      color: 'white',
    }}>
      {/* Header */}
      <header style={{ 
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <h1 style={{ margin: 0, fontSize: 24, color: colors.cyan }}>RECO AI</h1>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <div style={{ textAlign: 'right' }}>
            <p style={{ margin: 0, color: 'rgba(255,255,255,0.8)' }}>
              {profile?.full_name || user?.email || 'User'}
            </p>
            <p style={{ margin: 0, fontSize: 12, color: 'rgba(255,255,255,0.5)' }}>
              {profile?.role || 'User'}
            </p>
          </div>
          
          <button
            onClick={handleSignOut}
            style={{
              background: 'transparent',
              border: '1px solid rgba(255,255,255,0.2)',
              color: 'white',
              padding: '8px 16px',
              borderRadius: 8,
              cursor: 'pointer',
            }}
          >
            Sign Out
          </button>
        </div>
      </header>
      
      {/* Main Content */}
      <main style={{ padding: 24, maxWidth: 1200, margin: '0 auto' }}>
        {/* Stats Cards */}
        <div style={{ 
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(220px, 1fr))',
          gap: 24,
          marginBottom: 32,
        }}>
          <div style={{ 
            padding: 24,
            borderRadius: 12,
            ...glassMorphism,
          }}>
            <h3 style={{ margin: 0, marginBottom: 8, color: 'rgba(255,255,255,0.7)', fontSize: 16 }}>
              Total Receipts
            </h3>
            <p style={{ margin: 0, fontSize: 32, fontWeight: 600, color: colors.cyan }}>
              {stats.totalReceipts}
            </p>
          </div>
          
          <div style={{ 
            padding: 24,
            borderRadius: 12,
            ...glassMorphism,
          }}>
            <h3 style={{ margin: 0, marginBottom: 8, color: 'rgba(255,255,255,0.7)', fontSize: 16 }}>
              Total Amount
            </h3>
            <p style={{ margin: 0, fontSize: 32, fontWeight: 600, color: colors.cyan }}>
              ${stats.totalAmount.toFixed(2)}
            </p>
          </div>
          
          <div style={{ 
            padding: 24,
            borderRadius: 12,
            ...glassMorphism,
          }}>
            <h3 style={{ margin: 0, marginBottom: 8, color: 'rgba(255,255,255,0.7)', fontSize: 16 }}>
              This Month
            </h3>
            <p style={{ margin: 0, fontSize: 32, fontWeight: 600, color: colors.cyan }}>
              ${stats.thisMonth.toFixed(2)}
            </p>
          </div>
          
          <div style={{ 
            padding: 24,
            borderRadius: 12,
            ...glassMorphism,
          }}>
            <h3 style={{ margin: 0, marginBottom: 8, color: 'rgba(255,255,255,0.7)', fontSize: 16 }}>
              Last Month
            </h3>
            <p style={{ margin: 0, fontSize: 32, fontWeight: 600, color: colors.cyan }}>
              ${stats.lastMonth.toFixed(2)}
            </p>
          </div>
        </div>
        
        {/* Actions */}
        <div style={{ 
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}>
          <h2 style={{ margin: 0 }}>Your Receipts</h2>
          
          <button
            onClick={() => setShowUploadModal(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              padding: '12px 24px',
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              fontWeight: 600,
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="white" />
            </svg>
            Upload Receipt
          </button>
        </div>
        
        {/* Receipts Table */}
        <div style={{ 
          padding: 24,
          borderRadius: 12,
          ...glassMorphism,
          marginBottom: 32,
        }}>
          {receipts.length > 0 ? (
            <ReceiptTable receipts={receipts} />
          ) : (
            <div style={{ 
              textAlign: 'center', 
              padding: 48,
              color: 'rgba(255,255,255,0.6)',
            }}>
              <div style={{ 
                width: 80, 
                height: 80, 
                borderRadius: 40, 
                backgroundColor: 'rgba(0,212,255,0.1)', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                margin: '0 auto 16px',
              }}>
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19.5 3.5L18 2L16.5 3.5L15 2L13.5 3.5L12 2L10.5 3.5L9 2L7.5 3.5L6 2V16H3V19C3 20.66 4.34 22 6 22H18C19.66 22 21 20.66 21 19V2L19.5 3.5ZM19 19C19 19.55 18.55 20 18 20H6C5.45 20 5 19.55 5 19V18H19V19ZM19 16H8V5H19V16Z" fill={colors.cyan} />
                  <path d="M15 7H13V9H15V7Z" fill={colors.cyan} />
                  <path d="M15 10H13V12H15V10Z" fill={colors.cyan} />
                  <path d="M10 7H12V9H10V7Z" fill={colors.cyan} />
                  <path d="M10 10H12V12H10V10Z" fill={colors.cyan} />
                </svg>
              </div>
              <h3 style={{ marginBottom: 8 }}>No receipts yet</h3>
              <p style={{ marginBottom: 24 }}>
                Upload your first receipt to get started
              </p>
              <button
                onClick={() => setShowUploadModal(true)}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: 8,
                  padding: '12px 24px',
                  borderRadius: 8,
                  backgroundColor: colors.magenta,
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  fontWeight: 600,
                }}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="white" />
                </svg>
                Upload Receipt
              </button>
            </div>
          )}
        </div>
      </main>
      
      {/* Upload Modal */}
      {showUploadModal && (
        <UploadModal 
          onClose={() => setShowUploadModal(false)} 
          onUploadSuccess={handleUploadSuccess}
        />
      )}
    </div>
  );
}
