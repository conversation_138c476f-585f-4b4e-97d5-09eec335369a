import React from 'react';
import { Link } from 'react-router-dom';
import { colors, glassMorphism } from '../theme';

export default function LandingPage() {
  console.log('LandingPage component rendering');
  return (
    <div style={{ 
      minHeight: '100vh',
      background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
      color: 'white',
    }}>
      {/* Header */}
      <header style={{ 
        padding: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        maxWidth: 1200,
        margin: '0 auto',
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: 24, color: colors.cyan }}>RECO AI</h1>
        </div>
        
        <div>
          <Link 
            to="/login"
            style={{
              padding: '10px 24px',
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              textDecoration: 'none',
              fontWeight: 600,
            }}
          >
            Sign In
          </Link>
        </div>
      </header>
      
      {/* Hero Section */}
      <section style={{ 
        padding: '80px 24px',
        maxWidth: 1200,
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        textAlign: 'center',
      }}>
        <h1 style={{
          fontSize: 48,
          marginBottom: 24,
          background: `linear-gradient(90deg, ${colors.cyan} 0%, ${colors.magenta} 100%)`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          textFillColor: 'transparent',
        }}>
          🚀 UPDATED UI - Smart Receipt Management Powered by AI
        </h1>
        
        <p style={{ 
          fontSize: 20, 
          maxWidth: 700, 
          marginBottom: 40,
          color: 'rgba(255,255,255,0.8)',
        }}>
          Automatically extract data from receipts, organize expenses, and gain insights into your spending with our advanced AI technology.
        </p>
        
        <Link 
          to="/login"
          style={{
            padding: '16px 32px',
            borderRadius: 8,
            backgroundColor: colors.magenta,
            color: 'white',
            textDecoration: 'none',
            fontWeight: 600,
            fontSize: 18,
          }}
        >
          Get Started for Free
        </Link>
      </section>
      
      {/* Features Section */}
      <section style={{ 
        padding: '80px 24px',
        maxWidth: 1200,
        margin: '0 auto',
      }}>
        <h2 style={{ textAlign: 'center', marginBottom: 64 }}>Key Features</h2>
        
        <div style={{ 
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: 40,
        }}>
          <div style={{ 
            padding: 32,
            borderRadius: 16,
            ...glassMorphism,
          }}>
            <div style={{ 
              width: 64, 
              height: 64, 
              borderRadius: 32, 
              backgroundColor: 'rgba(0,212,255,0.1)', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              marginBottom: 24,
            }}>
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 16.2L4.8 12L3.4 13.4L9 19L21 7L19.6 5.6L9 16.2Z" fill={colors.cyan} />
              </svg>
            </div>
            <h3 style={{ marginBottom: 16 }}>Instant Data Extraction</h3>
            <p style={{ color: 'rgba(255,255,255,0.7)' }}>
              Our AI automatically extracts merchant names, dates, amounts, and line items from your receipts with high accuracy.
            </p>
          </div>
          
          <div style={{ 
            padding: 32,
            borderRadius: 16,
            ...glassMorphism,
          }}>
            <div style={{ 
              width: 64, 
              height: 64, 
              borderRadius: 32, 
              backgroundColor: 'rgba(0,212,255,0.1)', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              marginBottom: 24,
            }}>
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z" fill={colors.cyan} />
              </svg>
            </div>
            <h3 style={{ marginBottom: 16 }}>Expense Analytics</h3>
            <p style={{ color: 'rgba(255,255,255,0.7)' }}>
              Track spending patterns, categorize expenses, and visualize your financial data with intuitive dashboards.
            </p>
          </div>
          
          <div style={{ 
            padding: 32,
            borderRadius: 16,
            ...glassMorphism,
          }}>
            <div style={{ 
              width: 64, 
              height: 64, 
              borderRadius: 32, 
              backgroundColor: 'rgba(0,212,255,0.1)', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              marginBottom: 24,
            }}>
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4C9.11 4 6.6 5.64 5.35 8.04C2.34 8.36 0 10.91 0 14C0 17.31 2.69 20 6 20H19C21.76 20 24 17.76 24 15C24 12.36 21.95 10.22 19.35 10.04ZM19 18H6C3.79 18 2 16.21 2 14C2 11.95 3.53 10.24 5.56 10.03L6.63 9.92L7.13 8.97C8.08 7.14 9.94 6 12 6C14.62 6 16.88 7.86 17.39 10.43L17.69 11.93L19.22 12.04C20.78 12.14 22 13.45 22 15C22 16.65 20.65 18 19 18Z" fill={colors.cyan} />
              </svg>
            </div>
            <h3 style={{ marginBottom: 16 }}>Cloud Storage</h3>
            <p style={{ color: 'rgba(255,255,255,0.7)' }}>
              Securely store all your receipts in the cloud, accessible anytime, anywhere, from any device.
            </p>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section style={{ 
        padding: '80px 24px',
        maxWidth: 800,
        margin: '0 auto',
        textAlign: 'center',
      }}>
        <div style={{ 
          padding: 48,
          borderRadius: 16,
          ...glassMorphism,
          marginBottom: 80,
        }}>
          <h2 style={{ marginBottom: 24 }}>Ready to simplify your receipt management?</h2>
          <p style={{ 
            fontSize: 18, 
            marginBottom: 32,
            color: 'rgba(255,255,255,0.8)',
          }}>
            Join thousands of users who save time and gain financial insights with RECO AI.
          </p>
          
          <Link 
            to="/login"
            style={{
              padding: '16px 32px',
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              textDecoration: 'none',
              fontWeight: 600,
              fontSize: 18,
            }}
          >
            Get Started Now
          </Link>
        </div>
      </section>
      
      {/* Footer */}
      <footer style={{ 
        padding: '24px',
        borderTop: '1px solid rgba(255,255,255,0.1)',
        textAlign: 'center',
      }}>
        <p style={{ color: 'rgba(255,255,255,0.6)', margin: 0 }}>
          © {new Date().getFullYear()} RECO AI. All rights reserved.
        </p>
      </footer>
    </div>
  );
}
