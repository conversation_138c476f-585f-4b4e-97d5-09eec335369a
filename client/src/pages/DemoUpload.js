import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';
import ReceiptTable from '../components/ReceiptTable';
import { Link } from 'react-router-dom';

export default function DemoUpload({ onComplete, onReceiptUploaded }) {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [userToken, setUserToken] = useState(null);
  const [extractedReceipt, setExtractedReceipt] = useState(null);
  const [receiptsProcessed, setReceiptsProcessed] = useState(0);
  const [maxReceipts, setMaxReceipts] = useState(5); // Default to 5 for free tier
  const [showExportOptions, setShowExportOptions] = useState(false);

  useEffect(() => {
    const fetchSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUserToken(session.access_token);
      }
    };
    
    const fetchUserProfile = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        const { data, error } = await supabase
          .from('profiles')
          .select('receipt_count')
          .eq('id', session.user.id)
          .single();
          
        if (!error && data) {
          setReceiptsProcessed(data.receipt_count || 0);
        }
      }
    };
    
    fetchSession();
    fetchUserProfile();
  }, []);

  const handleFileChange = (e) => {
    const f = e.target.files[0];
    setFile(f);
    setPreview(f ? URL.createObjectURL(f) : null);
    setError('');
    setMessage('');
    setExtractedReceipt(null);
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file to upload.');
      return;
    }
    
    // Check if user has reached the limit
    if (receiptsProcessed >= maxReceipts) {
      setError(`You've reached your free tier limit of ${maxReceipts} receipts. Please upgrade to continue.`);
      setShowExportOptions(true);
      return;
    }
    
    setLoading(true);
    setError('');
    setMessage('');
    setExtractedReceipt(null);

    const formData = new FormData();
    formData.append('receipt', file);

    try {
      const response = await fetch('http://localhost:5000/api/upload-receipt', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
        },
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.upgradeRequired) {
          setError(`${data.error} Please upgrade to continue.`);
          setShowExportOptions(true);
          if (data.receiptsProcessed) {
            setReceiptsProcessed(data.receiptsProcessed);
          }
          return;
        }
        setError(data.error || 'Failed to upload receipt.');
        return;
      }

      setMessage('Receipt processed successfully!');
      setExtractedReceipt(data.savedReceipt);
      setReceiptsProcessed(prev => prev + 1);
      if (onReceiptUploaded) onReceiptUploaded();
      
      // Show export options if this was the last receipt
      if (receiptsProcessed + 1 >= maxReceipts) {
        setShowExportOptions(true);
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };

  // Progress calculation
  const progressPercentage = Math.min((receiptsProcessed / maxReceipts) * 100, 100);

  return (
    <div style={{ maxWidth: 900, margin: '0 auto', padding: 32, ...glassMorphism }}>
      <h2 style={{ color: colors.cyan, textAlign: 'center', marginBottom: 24 }}>
        Try RECO AI: Upload a Receipt
      </h2>
      
      {/* Progress Bar */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <span style={{ color: colors.cyan }}>Step 2 of 4: Upload Receipts</span>
          <span style={{ color: colors.magenta }}>
            {receiptsProcessed} of {maxReceipts} receipts processed
          </span>
        </div>
        <div style={{ 
          width: '100%', 
          height: 8, 
          backgroundColor: 'rgba(255,255,255,0.1)', 
          borderRadius: 4,
          overflow: 'hidden'
        }}>
          <div style={{ 
            width: `${progressPercentage}%`, 
            height: '100%', 
            backgroundColor: colors.cyan,
            borderRadius: 4,
            transition: 'width 0.3s ease'
          }} />
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 16 }}>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          style={{ display: 'none' }}
          id="receipt-upload"
          disabled={loading || receiptsProcessed >= maxReceipts}
        />
        <label
          htmlFor="receipt-upload"
          style={{
            padding: '12px 24px',
            borderRadius: 8,
            background: receiptsProcessed >= maxReceipts ? '#888' : colors.cyan,
            color: colors.charcoal,
            fontWeight: 700,
            cursor: receiptsProcessed >= maxReceipts ? 'not-allowed' : 'pointer',
            textAlign: 'center',
            width: '100%',
            maxWidth: 300,
          }}
        >
          {receiptsProcessed >= maxReceipts ? 'Free Tier Limit Reached' : 'Select Receipt Image'}
        </label>

        {preview && (
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <img
              src={preview}
              alt="Receipt preview"
              style={{ maxWidth: '100%', maxHeight: 300, borderRadius: 8 }}
            />
          </div>
        )}

        {file && (
          <button
            onClick={handleUpload}
            disabled={loading || receiptsProcessed >= maxReceipts}
            style={{
              padding: '12px 24px',
              borderRadius: 8,
              background: loading || receiptsProcessed >= maxReceipts ? '#888' : colors.magenta,
              color: 'white',
              fontWeight: 700,
              border: 'none',
              cursor: loading || receiptsProcessed >= maxReceipts ? 'not-allowed' : 'pointer',
              width: '100%',
              maxWidth: 300,
            }}
          >
            {loading ? 'Processing...' : 'Process Receipt'}
          </button>
        )}
      </div>

      {error && <div style={{ color: colors.error, marginTop: 16, textAlign: 'center' }}>{error}</div>}
      {message && <div style={{ color: colors.success, marginTop: 16, textAlign: 'center' }}>{message}</div>}

      {extractedReceipt && (
        <div style={{ marginTop: 40 }}>
          <h3 style={{ color: colors.cyan, marginBottom: 20, textAlign: 'center' }}>
            {receiptsProcessed === 1 ? 'Great! Your first receipt is processed.' : `Nice! ${receiptsProcessed} of ${maxReceipts} receipts processed.`}
          </h3>
          <ReceiptTable receipts={[extractedReceipt]} />
          
          {receiptsProcessed < maxReceipts && (
            <div style={{ textAlign: 'center', marginTop: 24, color: colors.cyan }}>
              <p>You have {maxReceipts - receiptsProcessed} uploads left in your free trial.</p>
              <p>Upload more receipts or continue to your dashboard.</p>
            </div>
          )}
        </div>
      )}
      
      {/* Export Options or Upgrade Prompt */}
      {(showExportOptions || receiptsProcessed >= maxReceipts) && (
        <div style={{ 
          marginTop: 32, 
          padding: 24, 
          borderRadius: 8, 
          backgroundColor: 'rgba(0,0,0,0.2)',
          borderLeft: `4px solid ${colors.magenta}`
        }}>
          <h3 style={{ color: colors.magenta, marginBottom: 16 }}>
            {receiptsProcessed >= maxReceipts ? 
              "You've reached your free tier limit!" : 
              "Don't lose your data!"}
          </h3>
          <p style={{ color: '#fff', marginBottom: 16 }}>
            {receiptsProcessed >= maxReceipts ?
              `You've processed ${receiptsProcessed} receipts. Upgrade now to continue using RECO AI and unlock unlimited exports and insights.` :
              `You've processed ${receiptsProcessed} of ${maxReceipts} receipts. Export your data now or upgrade to keep going.`}
          </p>
          
          <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap', justifyContent: 'center', marginTop: 24 }}>
            <button
              onClick={onComplete}
              style={{
                padding: '12px 24px',
                borderRadius: 8,
                background: colors.cyan,
                color: colors.charcoal,
                fontWeight: 700,
                border: 'none',
                cursor: 'pointer',
              }}
            >
              Continue to Dashboard
            </button>
            
            <button
              style={{
                padding: '12px 24px',
                borderRadius: 8,
                background: colors.magenta,
                color: 'white',
                fontWeight: 700,
                border: 'none',
                cursor: 'pointer',
              }}
              onClick={() => {
                // This would be handled in the Dashboard component
                onComplete();
              }}
            >
              Upgrade Now
            </button>
          </div>
        </div>
      )}
      
      {/* Continue button at bottom */}
      {extractedReceipt && receiptsProcessed < maxReceipts && (
        <button
          style={{
            marginTop: 32,
            padding: 16,
            width: '100%',
            borderRadius: 8,
            background: colors.success,
            color: colors.charcoal,
            fontWeight: 700,
            fontSize: 18,
            border: 'none',
            cursor: 'pointer',
            transition: 'background 0.3s ease',
          }}
          onClick={onComplete}
        >
          Continue to Dashboard
        </button>
      )}
    </div>
  );
}
