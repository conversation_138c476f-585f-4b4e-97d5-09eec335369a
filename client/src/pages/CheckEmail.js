import React from 'react';
import { Link } from 'react-router-dom';
import { colors, glassMorphism } from '../theme';

export default function CheckEmail() {
  return (
    <div style={{ 
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 24,
      background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
    }}>
      <div style={{ 
        maxWidth: 500,
        width: '100%',
        padding: 40,
        borderRadius: 16,
        textAlign: 'center',
        ...glassMorphism,
      }}>
        <div style={{ 
          width: 80, 
          height: 80, 
          borderRadius: 40, 
          backgroundColor: 'rgba(0,212,255,0.1)', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          margin: '0 auto 24px',
        }}>
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill={colors.cyan} />
          </svg>
        </div>
        
        <h1 style={{ color: 'white', marginBottom: 16 }}>Check Your Email</h1>
        
        <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 24 }}>
          We've sent you a magic link to sign in to your account.
          <br />
          Please check your inbox and click the link to continue.
        </p>
        
        <div style={{ marginBottom: 32, padding: 16, backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 8 }}>
          <p style={{ color: 'rgba(255,255,255,0.6)', margin: 0 }}>
            The link will expire in 24 hours. If you don't see the email, check your spam folder.
          </p>
        </div>
        
        <Link 
          to="/login"
          style={{
            display: 'inline-block',
            padding: '12px 24px',
            borderRadius: 8,
            backgroundColor: 'rgba(255,255,255,0.1)',
            color: 'white',
            textDecoration: 'none',
            fontWeight: 500,
          }}
        >
          Back to Login
        </Link>
      </div>
    </div>
  );
}
