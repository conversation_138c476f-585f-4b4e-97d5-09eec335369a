import React, { useState } from 'react';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';

export default function SignIn({ onAuth }) {
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  const handleEmailAuth = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    let authResponse;
    if (isSignUp) {
      authResponse = await supabase.auth.signUp({ email, password });
    } else {
      authResponse = await supabase.auth.signInWithPassword({ email, password });
    }

    setLoading(false);
    if (authResponse.error) {
      setError(authResponse.error.message);
    } else if (authResponse.data.user) {
      if (isSignUp) {
      }
      // Bypassing email confirmation and profile creation for automation purposes
      if (onAuth) onAuth(authResponse.data.user);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError('');
    setMessage('');
    const { error } = await supabase.auth.signInWithOAuth({ provider: 'google' });
    setLoading(false);
    if (error) setError(error.message);
  };

  return (
    <div style={{ maxWidth: 400, margin: '0 auto', padding: 32, ...glassMorphism }}>
      <h2 style={{ color: colors.cyan, textAlign: 'center', marginBottom: 24 }}>{isSignUp ? 'Sign Up' : 'Sign In'}</h2>
      <form onSubmit={handleEmailAuth} style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          required
          style={{ padding: 12, borderRadius: 8, border: `1px solid ${colors.primary}`, background: 'rgba(255,255,255,0.1)', color: '#fff' }}
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
          style={{ padding: 12, borderRadius: 8, border: `1px solid ${colors.primary}`, background: 'rgba(255,255,255,0.1)', color: '#fff' }}
        />
        <button type="submit" style={{ padding: 16, borderRadius: 8, background: colors.cyan, color: colors.charcoal, fontWeight: 700, fontSize: 18, border: 'none', cursor: 'pointer', opacity: loading ? 0.5 : 1 }} disabled={loading}>
          {loading ? (isSignUp ? 'Signing up...' : 'Signing in...') : (isSignUp ? 'Sign Up' : 'Sign In')}
        </button>
      </form>
      <button onClick={handleGoogleSignIn} style={{ marginTop: 24, padding: 16, borderRadius: 8, background: '#fff', color: colors.navy, fontWeight: 700, fontSize: 18, border: 'none', cursor: 'pointer', boxShadow: `0 0 8px ${colors.cyan}33` }} disabled={loading}>
        Sign {isSignUp ? 'up' : 'in'} with Google
      </button>
      {error && <div style={{ color: colors.red, marginTop: 16, textAlign: 'center' }}>{error}</div>}
      {message && <div style={{ color: colors.green, marginTop: 16, textAlign: 'center' }}>{message}</div>}
      <p style={{ textAlign: 'center', marginTop: 24, color: '#aaa' }}>
        {isSignUp ? 'Already have an account?' : 'Don\'t have an account?'}
        <span
          onClick={() => setIsSignUp(!isSignUp)}
          style={{ color: colors.magenta, cursor: 'pointer', marginLeft: 8, fontWeight: 600 }}
        >
          {isSignUp ? 'Sign In' : 'Sign Up'}
        </span>
      </p>
    </div>
  );
}
