import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';

export default function Login() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address');
      return;
    }
    
    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: window.location.origin,
        },
      });
      
      if (error) throw error;
      
      setSuccess(true);
      navigate('/check-email');
    } catch (error) {
      console.error('Error sending magic link:', error);
      setError(error.message || 'Failed to send login link');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 24,
      background: `linear-gradient(135deg, ${colors.charcoal} 0%, ${colors.navy} 100%)`,
    }}>
      <div style={{ 
        maxWidth: 400,
        width: '100%',
        padding: 40,
        borderRadius: 16,
        ...glassMorphism,
      }}>
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <h1 style={{ color: colors.cyan, marginBottom: 8 }}>RECO AI</h1>
          <p style={{ color: 'rgba(255,255,255,0.8)' }}>
            Sign in to manage your receipts
          </p>
        </div>
        
        <form onSubmit={handleLogin}>
          <div style={{ marginBottom: 24 }}>
            <label style={{ display: 'block', color: 'white', marginBottom: 8 }}>
              Email Address
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              style={{
                padding: 16,
                borderRadius: 8,
                border: '1px solid rgba(255,255,255,0.2)',
                backgroundColor: 'rgba(0,0,0,0.2)',
                color: 'white',
                fontSize: 16,
                width: '100%',
              }}
            />
          </div>
          
          {error && (
            <div style={{ color: colors.error, marginBottom: 16 }}>
              {error}
            </div>
          )}
          
          <button
            type="submit"
            disabled={loading}
            style={{
              width: '100%',
              padding: 16,
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              border: 'none',
              fontSize: 16,
              fontWeight: 600,
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.7 : 1,
            }}
          >
            {loading ? 'Sending...' : 'Sign In with Magic Link'}
          </button>
        </form>
        
        <p style={{ 
          textAlign: 'center', 
          marginTop: 24, 
          color: 'rgba(255,255,255,0.6)',
          fontSize: 14,
        }}>
          We'll send you a magic link to your email.
          <br />
          No password required!
        </p>
      </div>
    </div>
  );
}