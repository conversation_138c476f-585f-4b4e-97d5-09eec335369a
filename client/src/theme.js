// Theme colors and styles for the application

export const colors = {
  // Primary colors
  cyan: '#00D4FF',
  magenta: '#FF00E5',
  
  // Background colors
  charcoal: '#121212',
  navy: '#0A1929',
  
  // UI colors
  success: '#00C853',
  warning: '#FFD600',
  error: '#FF3D00',
  
  // Text colors
  textPrimary: '#FFFFFF',
  textSecondary: 'rgba(255,255,255,0.7)',
  textDisabled: 'rgba(255,255,255,0.5)',
};

// Glass morphism effect used throughout the app
export const glassMorphism = {
  backgroundColor: 'rgba(0,0,0,0.3)',
  backdropFilter: 'blur(10px)',
  boxShadow: '0 8px 32px 0 rgba(0, 0, 0, 0.37)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
};

// Common button styles
export const buttonStyles = {
  primary: {
    backgroundColor: colors.magenta,
    color: colors.textPrimary,
    border: 'none',
    borderRadius: 8,
    padding: '12px 24px',
    fontWeight: 600,
    cursor: 'pointer',
  },
  secondary: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    color: colors.textPrimary,
    border: 'none',
    borderRadius: 8,
    padding: '12px 24px',
    fontWeight: 500,
    cursor: 'pointer',
  },
  text: {
    backgroundColor: 'transparent',
    color: colors.cyan,
    border: 'none',
    padding: '8px 16px',
    cursor: 'pointer',
  },
};

// Common input styles
export const inputStyles = {
  standard: {
    padding: 16,
    borderRadius: 8,
    border: '1px solid rgba(255,255,255,0.2)',
    backgroundColor: 'rgba(0,0,0,0.2)',
    color: colors.textPrimary,
    fontSize: 16,
    width: '100%',
  },
};
