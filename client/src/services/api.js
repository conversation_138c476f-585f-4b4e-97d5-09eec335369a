// Fetch user receipts
export const fetchReceipts = async () => {
  try {
    const token = await getToken();
    const response = await fetch(`${API_URL}/api/receipts`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch receipts');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching receipts:', error);
    throw error;
  }
};