import React, { useState } from 'react';
import { colors } from '../theme';

export default function ReceiptTable({ receipts }) {
  const [expandedRow, setExpandedRow] = useState(null);
  
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return 'N/A';
    return `$${parseFloat(amount).toFixed(2)}`;
  };
  
  const toggleRow = (id) => {
    if (expandedRow === id) {
      setExpandedRow(null);
    } else {
      setExpandedRow(id);
    }
  };

  return (
    <div style={{ overflowX: 'auto' }}>
      <table style={{ 
        width: '100%', 
        borderCollapse: 'collapse',
        color: 'white',
      }}>
        <thead>
          <tr style={{ borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
            <th style={{ padding: '16px 12px', textAlign: 'left' }}>Date</th>
            <th style={{ padding: '16px 12px', textAlign: 'left' }}>Merchant</th>
            <th style={{ padding: '16px 12px', textAlign: 'left' }}>Category</th>
            <th style={{ padding: '16px 12px', textAlign: 'right' }}>Amount</th>
            <th style={{ padding: '16px 12px', textAlign: 'center', width: 80 }}>Actions</th>
          </tr>
        </thead>
        <tbody>
          {receipts.map((receipt) => (
            <React.Fragment key={receipt.id}>
              <tr 
                style={{ 
                  borderBottom: '1px solid rgba(255,255,255,0.1)',
                  backgroundColor: expandedRow === receipt.id ? 'rgba(0,212,255,0.05)' : 'transparent',
                  cursor: 'pointer',
                }}
                onClick={() => toggleRow(receipt.id)}
              >
                <td style={{ padding: '16px 12px' }}>{formatDate(receipt.date)}</td>
                <td style={{ padding: '16px 12px' }}>{receipt.merchant || 'Unknown'}</td>
                <td style={{ padding: '16px 12px' }}>
                  <span style={{
                    display: 'inline-block',
                    padding: '4px 12px',
                    borderRadius: 16,
                    backgroundColor: 'rgba(0,212,255,0.1)',
                    color: colors.cyan,
                    fontSize: 14,
                  }}>
                    {receipt.category || 'Uncategorized'}
                  </span>
                </td>
                <td style={{ padding: '16px 12px', textAlign: 'right', fontWeight: 600 }}>
                  {formatCurrency(receipt.amount)}
                </td>
                <td style={{ padding: '16px 12px', textAlign: 'center' }}>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleRow(receipt.id);
                    }}
                    style={{
                      background: 'transparent',
                      border: 'none',
                      color: 'white',
                      cursor: 'pointer',
                    }}
                  >
                    <svg 
                      width="24" 
                      height="24" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      xmlns="http://www.w3.org/2000/svg"
                      style={{
                        transform: expandedRow === receipt.id ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.3s ease',
                      }}
                    >
                      <path d="M7 10L12 15L17 10H7Z" fill="white" />
                    </svg>
                  </button>
                </td>
              </tr>
              {expandedRow === receipt.id && (
                <tr style={{ backgroundColor: 'rgba(0,0,0,0.2)' }}>
                  <td colSpan={5} style={{ padding: 24 }}>
                    <div style={{ display: 'flex', gap: 24 }}>
                      {receipt.image_url && (
                        <div style={{ flex: '0 0 200px' }}>
                          <img 
                            src={receipt.image_url} 
                            alt="Receipt" 
                            style={{ 
                              maxWidth: '100%', 
                              maxHeight: 300, 
                              borderRadius: 8,
                              border: '1px solid rgba(255,255,255,0.1)'
                            }} 
                          />
                        </div>
                      )}
                      
                      <div style={{ flex: 1 }}>
                        <h3 style={{ marginTop: 0, marginBottom: 16 }}>Receipt Details</h3>
                        
                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px 24px' }}>
                          <div>
                            <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Merchant</p>
                            <p style={{ margin: 0, fontWeight: 500 }}>{receipt.merchant || 'Unknown'}</p>
                          </div>
                          
                          <div>
                            <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Date</p>
                            <p style={{ margin: 0, fontWeight: 500 }}>{formatDate(receipt.date)}</p>
                          </div>
                          
                          <div>
                            <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Amount</p>
                            <p style={{ margin: 0, fontWeight: 600 }}>{formatCurrency(receipt.amount)}</p>
                          </div>
                          
                          <div>
                            <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Category</p>
                            <p style={{ margin: 0, fontWeight: 500 }}>{receipt.category || 'Uncategorized'}</p>
                          </div>
                          
                          {receipt.tax_amount && (
                            <div>
                              <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Tax</p>
                              <p style={{ margin: 0, fontWeight: 500 }}>{formatCurrency(receipt.tax_amount)}</p>
                            </div>
                          )}
                          
                          {receipt.tip_amount && (
                            <div>
                              <p style={{ color: 'rgba(255,255,255,0.6)', margin: '0 0 4px 0', fontSize: 14 }}>Tip</p>
                              <p style={{ margin: 0, fontWeight: 500 }}>{formatCurrency(receipt.tip_amount)}</p>
                            </div>
                          )}
                        </div>
                        
                        {receipt.items && receipt.items.length > 0 && (
                          <div style={{ marginTop: 24 }}>
                            <h4 style={{ marginTop: 0, marginBottom: 12 }}>Items</h4>
                            <div style={{ 
                              backgroundColor: 'rgba(0,0,0,0.2)', 
                              borderRadius: 8, 
                              padding: 16,
                              maxHeight: 200,
                              overflowY: 'auto',
                            }}>
                              {receipt.items.map((item, index) => (
                                <div 
                                  key={index}
                                  style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between',
                                    padding: '8px 0',
                                    borderBottom: index < receipt.items.length - 1 ? '1px solid rgba(255,255,255,0.1)' : 'none',
                                  }}
                                >
                                  <div>
                                    <p style={{ margin: 0, fontWeight: 500 }}>{item.name || `Item ${index + 1}`}</p>
                                    {item.quantity && (
                                      <p style={{ margin: '4px 0 0 0', fontSize: 14, color: 'rgba(255,255,255,0.6)' }}>
                                        Qty: {item.quantity}
                                      </p>
                                    )}
                                  </div>
                                  <p style={{ margin: 0, fontWeight: 500 }}>{formatCurrency(item.price)}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {receipt.notes && (
                          <div style={{ marginTop: 24 }}>
                            <h4 style={{ marginTop: 0, marginBottom: 8 }}>Notes</h4>
                            <p style={{ 
                              margin: 0, 
                              padding: 16, 
                              backgroundColor: 'rgba(0,0,0,0.2)', 
                              borderRadius: 8,
                              color: 'rgba(255,255,255,0.8)',
                            }}>
                              {receipt.notes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
}
