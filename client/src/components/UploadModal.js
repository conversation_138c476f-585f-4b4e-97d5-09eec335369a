import React, { useState } from 'react';
import { supabase } from '../supabaseClient';
import { colors, glassMorphism } from '../theme';

export default function UploadModal({ onClose, onUploadSuccess }) {
  const [file, setFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file) => {
    // Check if file is an image
    if (!file.type.match('image.*')) {
      setError('Please upload an image file (JPEG, PNG, etc.)');
      return;
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size exceeds 5MB limit');
      return;
    }
    
    setFile(file);
    setError('');
  };

  const uploadReceipt = async () => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    setUploading(true);
    setError('');
    setUploadProgress(0);

    try {
      // Get user authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('User not authenticated');

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('receipt', file);

      setUploadProgress(20); // Starting upload

      // Upload directly to server endpoint
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/upload-receipt`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: formData,
      });

      setUploadProgress(80);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process receipt');
      }

      const receiptData = await response.json();
      setUploadProgress(100);

      // Notify parent component of successful upload
      onUploadSuccess(receiptData.savedReceipt || receiptData);

    } catch (error) {
      console.error('Error uploading receipt:', error);
      setError(error.message || 'Failed to upload receipt');
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.7)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: 16,
    }}>
      <div style={{
        maxWidth: 500,
        width: '100%',
        borderRadius: 16,
        padding: 32,
        ...glassMorphism,
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <h2 style={{ color: 'white', margin: 0 }}>Upload Receipt</h2>
          <button 
            onClick={onClose}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'white',
              fontSize: 24,
              cursor: 'pointer',
            }}
          >
            ×
          </button>
        </div>
        
        <div 
          style={{
            border: `2px dashed ${dragActive ? colors.cyan : 'rgba(255,255,255,0.2)'}`,
            borderRadius: 8,
            padding: 32,
            textAlign: 'center',
            backgroundColor: dragActive ? 'rgba(0,212,255,0.05)' : 'rgba(0,0,0,0.2)',
            marginBottom: 24,
            cursor: 'pointer',
          }}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => document.getElementById('file-upload').click()}
        >
          {file ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <img 
                  src={URL.createObjectURL(file)} 
                  alt="Receipt preview" 
                  style={{ 
                    maxWidth: '100%', 
                    maxHeight: 200, 
                    borderRadius: 8,
                  }} 
                />
              </div>
              <p style={{ color: 'white', margin: 0 }}>{file.name}</p>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setFile(null);
                }}
                style={{
                  background: 'rgba(255,255,255,0.1)',
                  border: 'none',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: 4,
                  marginTop: 8,
                  cursor: 'pointer',
                }}
              >
                Remove
              </button>
            </div>
          ) : (
            <div>
              <div style={{ 
                width: 64, 
                height: 64, 
                borderRadius: 32, 
                backgroundColor: 'rgba(0,212,255,0.1)', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                margin: '0 auto 16px',
              }}>
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 13V19H5V13H3V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V13H19ZM13 13V7H16L12 3L8 7H11V13H13Z" fill={colors.cyan} />
                </svg>
              </div>
              <p style={{ color: 'white', marginBottom: 8 }}>
                Drag & drop your receipt image here
              </p>
              <p style={{ color: 'rgba(255,255,255,0.6)', fontSize: 14, margin: 0 }}>
                or click to browse files
              </p>
            </div>
          )}
          
          <input
            id="file-upload"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
        </div>
        
        {error && (
          <div style={{ color: colors.error, marginBottom: 16, textAlign: 'center' }}>
            {error}
          </div>
        )}
        
        {uploading && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ 
              height: 8, 
              backgroundColor: 'rgba(255,255,255,0.1)', 
              borderRadius: 4, 
              overflow: 'hidden',
              marginBottom: 8,
            }}>
              <div style={{ 
                height: '100%', 
                width: `${uploadProgress}%`, 
                backgroundColor: colors.cyan,
                borderRadius: 4,
                transition: 'width 0.3s ease',
              }} />
            </div>
            <p style={{ color: 'rgba(255,255,255,0.6)', fontSize: 14, margin: 0, textAlign: 'center' }}>
              {uploadProgress < 50 ? 'Uploading...' : 
               uploadProgress < 80 ? 'Processing receipt...' : 
               'Finalizing...'}
            </p>
          </div>
        )}
        
        <div style={{ display: 'flex', gap: 16 }}>
          <button
            onClick={onClose}
            style={{
              flex: 1,
              padding: '12px',
              borderRadius: 8,
              backgroundColor: 'rgba(255,255,255,0.1)',
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              fontWeight: 500,
            }}
          >
            Cancel
          </button>
          
          <button
            onClick={uploadReceipt}
            disabled={!file || uploading}
            style={{
              flex: 2,
              padding: '12px',
              borderRadius: 8,
              backgroundColor: colors.magenta,
              color: 'white',
              border: 'none',
              cursor: !file || uploading ? 'not-allowed' : 'pointer',
              opacity: !file || uploading ? 0.7 : 1,
              fontWeight: 600,
            }}
          >
            {uploading ? 'Processing...' : 'Upload Receipt'}
          </button>
        </div>
      </div>
    </div>
  );
}
