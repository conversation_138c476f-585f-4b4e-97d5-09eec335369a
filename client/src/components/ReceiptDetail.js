import React from 'react';
import { colors, glassMorphism } from '../theme';

export default function ReceiptDetail({ receipt }) {
  if (!receipt) {
    return null;
  }

  return (
    <div style={{ 
      padding: 20, 
      marginTop: 20, 
      ...glassMorphism,
      color: colors.white 
    }}>
      <h2 style={{ color: colors.cyan, marginBottom: 20 }}>Receipt Details</h2>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 20 }}>
        <div>
          <h3 style={{ color: colors.secondary }}>Basic Information</h3>
          <p><strong>Vendor:</strong> {receipt.vendor || 'N/A'}</p>
          <p><strong>Vendor Tax ID:</strong> {receipt.vendor_tax_id || 'N/A'}</p>
          <p><strong>Date:</strong> {receipt.date || 'N/A'}</p>
          <p><strong>Category:</strong> {receipt.category || 'N/A'}</p>
          <p><strong>Payment Method:</strong> {receipt.payment_method || 'N/A'}</p>
        </div>
        
        <div>
          <h3 style={{ color: colors.secondary }}>Financial Details</h3>
          <p><strong>Subtotal:</strong> ${parseFloat(receipt.subtotal || 0).toFixed(2)}</p>
          <p><strong>Tax Rate:</strong> {parseFloat(receipt.tax_rate_percent || 0).toFixed(2)}%</p>
          <p><strong>Tax Amount:</strong> ${parseFloat(receipt.tax_amount || 0).toFixed(2)}</p>
          <p><strong>Total Amount:</strong> ${parseFloat(receipt.total_amount || receipt.amount || 0).toFixed(2)}</p>
          <p><strong>Source:</strong> {receipt.source_file || 'Manual Upload'}</p>
        </div>
      </div>
      
      <div style={{ marginTop: 20 }}>
        <h3 style={{ color: colors.secondary }}>Items</h3>
        {receipt.items && receipt.items.length > 0 ? (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr>
                <th style={{ textAlign: 'left', padding: '8px 12px', borderBottom: `1px solid ${colors.primary}` }}>Description</th>
                <th style={{ textAlign: 'left', padding: '8px 12px', borderBottom: `1px solid ${colors.primary}` }}>Category</th>
                <th style={{ textAlign: 'right', padding: '8px 12px', borderBottom: `1px solid ${colors.primary}` }}>Amount</th>
              </tr>
            </thead>
            <tbody>
              {receipt.items.map((item, index) => (
                <tr key={index}>
                  <td style={{ padding: '8px 12px', borderBottom: `1px solid ${colors.primary}` }}>{item.description || item.name || 'N/A'}</td>
                  <td style={{ padding: '8px 12px', borderBottom: `1px solid ${colors.primary}` }}>{item.category || 'Uncategorized'}</td>
                  <td style={{ padding: '8px 12px', borderBottom: `1px solid ${colors.primary}`, textAlign: 'right' }}>${parseFloat(item.total || item.price || 0).toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No items found on this receipt.</p>
        )}
      </div>
    </div>
  );
}