-- Create storage bucket for receipt images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'receipt-images',
  'receipt-images',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
);

-- Create policy to allow authenticated users to upload their own files
CREATE POLICY "Users can upload their own receipt images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'receipt-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy to allow authenticated users to view their own files
CREATE POLICY "Users can view their own receipt images" ON storage.objects
FOR SELECT USING (
  bucket_id = 'receipt-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy to allow authenticated users to delete their own files
CREATE POLICY "Users can delete their own receipt images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'receipt-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
